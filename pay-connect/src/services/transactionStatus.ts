import api from './api';

export interface TransactionStatus {
  id: number;
  transactionId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  details: string;
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
}

// Event bus for real-time status updates
type StatusListener = (status: TransactionStatus) => void;
const statusListeners: Record<string, StatusListener[]> = {};

/**
 * Get the current status of a transaction
 * @param transactionId The ID of the transaction to check
 * @returns The current status of the transaction
 */
export const getTransactionStatus = async (transactionId: number | string): Promise<TransactionStatus> => {
  try {
    const response = await api.get(`/transactions/${transactionId}/status`);
    return response.data.data;
  } catch (error) {
    console.error(`Error fetching transaction status for transaction ${transactionId}:`, error);
    throw error;
  }
};

/**
 * Poll for transaction status updates
 * @param transactionId The ID of the transaction to poll
 * @param onStatusChange Callback function to handle status changes
 * @param interval Polling interval in milliseconds (default: 2000)
 * @param maxAttempts Maximum number of polling attempts (default: 30)
 * @returns A function to stop polling
 */
export const pollTransactionStatus = (
  transactionId: number | string,
  onStatusChange: (status: TransactionStatus) => void,
  interval = 2000,
  maxAttempts = 30
): { stop: () => void } => {
  let attempts = 0;
  let timerId: NodeJS.Timeout | null = null;
  let lastStatus: string | null = null;

  const checkStatus = async () => {
    try {
      const status = await getTransactionStatus(transactionId);
      
      // Only call the callback if the status has changed
      if (lastStatus !== status.status) {
        lastStatus = status.status;
        onStatusChange(status);
      }
      
      // Stop polling if the transaction is completed or failed
      if (status.status === 'completed' || status.status === 'failed') {
        if (timerId) {
          clearTimeout(timerId);
          timerId = null;
        }
        return;
      }
      
      // Stop polling if we've reached the maximum number of attempts
      attempts++;
      if (attempts >= maxAttempts) {
        if (timerId) {
          clearTimeout(timerId);
          timerId = null;
        }
        return;
      }
      
      // Schedule the next poll
      timerId = setTimeout(checkStatus, interval);
    } catch (error) {
      console.error(`Error polling transaction status for transaction ${transactionId}:`, error);
      
      // Stop polling on error
      if (timerId) {
        clearTimeout(timerId);
        timerId = null;
      }
    }
  };
  
  // Start polling
  checkStatus();
  
  // Return a function to stop polling
  return {
    stop: () => {
      if (timerId) {
        clearTimeout(timerId);
        timerId = null;
      }
    }
  };
};

/**
 * Update transaction status in the database
 * @param transactionId The ID of the transaction to update
 * @param status The new status
 * @param details Details about the status change
 * @param metadata Additional metadata
 * @returns The updated transaction status
 */
export const updateTransactionStatus = async (
  transactionId: number | string,
  status: TransactionStatus['status'],
  details: string,
  metadata?: Record<string, any>
): Promise<TransactionStatus> => {
  try {
    const response = await api.put(`/transactions/${transactionId}/status`, {
      status,
      details,
      metadata
    });
    
    const updatedStatus = response.data.data;
    
    // Notify all listeners about the status change
    notifyStatusChange(transactionId.toString(), updatedStatus);
    
    return updatedStatus;
  } catch (error) {
    console.error(`Error updating transaction status for transaction ${transactionId}:`, error);
    throw error;
  }
};

/**
 * Handle Plaid webhook events for transaction status updates
 * @param webhookEvent The webhook event from Plaid
 * @returns The updated transaction status
 */
export const handlePlaidWebhook = async (webhookEvent: any): Promise<TransactionStatus | null> => {
  try {
    // Extract the transaction ID from the webhook event
    const transactionId = webhookEvent.data?.transaction_id;
    if (!transactionId) {
      console.error('No transaction ID found in webhook event:', webhookEvent);
      return null;
    }
    
    // Get the current status of the transaction
    const status = await getTransactionStatus(transactionId);
    
    // Update the transaction status based on the webhook event
    let newStatus: TransactionStatus['status'] = status.status;
    let details = status.details;
    
    switch (webhookEvent.type) {
      case 'TRANSFER_CREATED':
        newStatus = 'pending';
        details = 'Transfer created';
        break;
      case 'TRANSFER_PENDING':
        newStatus = 'processing';
        details = 'Transfer pending';
        break;
      case 'TRANSFER_EXECUTED':
        newStatus = 'processing';
        details = 'Transfer executed';
        break;
      case 'TRANSFER_SETTLED':
        newStatus = 'completed';
        details = 'Transfer settled';
        break;
      case 'TRANSFER_FAILED':
        newStatus = 'failed';
        details = webhookEvent.data?.failure_reason || 'Transfer failed';
        break;
      case 'TRANSFER_CANCELLED':
        newStatus = 'failed';
        details = 'Transfer cancelled';
        break;
      case 'TRANSFER_RETURNED':
        newStatus = 'failed';
        details = 'Transfer returned: ' + (webhookEvent.data?.return_reason || 'Unknown reason');
        break;
      case 'TRANSFER_SWEEPING':
        newStatus = 'processing';
        details = 'Transfer sweeping';
        break;
      case 'TRANSFER_SWEPT':
        newStatus = 'completed';
        details = 'Transfer swept';
        break;
      default:
        // No status update for unknown webhook events
        return status;
    }
    
    // Only update if the status has changed
    if (newStatus !== status.status || details !== status.details) {
      return await updateTransactionStatus(transactionId, newStatus, details, webhookEvent.data);
    }
    
    return status;
  } catch (error) {
    console.error('Error handling Plaid webhook:', error);
    return null;
  }
};

/**
 * Notify all listeners about a status change
 * @param transactionId The ID of the transaction
 * @param status The new status
 */
const notifyStatusChange = (transactionId: string, status: TransactionStatus): void => {
  const listeners = statusListeners[transactionId] || [];
  listeners.forEach(listener => {
    try {
      listener(status);
    } catch (error) {
      console.error(`Error notifying listener for transaction ${transactionId}:`, error);
    }
  });
};

/**
 * Register a callback for real-time transaction status updates
 * @param transactionId The ID of the transaction to watch
 * @param onStatusChange Callback function to handle status changes
 * @returns A function to unregister the callback
 */
export const watchTransactionStatus = (
  transactionId: number | string,
  onStatusChange: (status: TransactionStatus) => void
): { unwatch: () => void } => {
  const transactionIdStr = transactionId.toString();
  
  // Initialize the listeners array if it doesn't exist
  if (!statusListeners[transactionIdStr]) {
    statusListeners[transactionIdStr] = [];
  }
  
  // Add the listener
  statusListeners[transactionIdStr].push(onStatusChange);
  
  // Start polling as a fallback
  const { stop } = pollTransactionStatus(transactionId, onStatusChange);
  
  // Return a function to unregister the callback
  return {
    unwatch: () => {
      // Remove the listener
      if (statusListeners[transactionIdStr]) {
        statusListeners[transactionIdStr] = statusListeners[transactionIdStr].filter(
          listener => listener !== onStatusChange
        );
        
        // Clean up if there are no more listeners
        if (statusListeners[transactionIdStr].length === 0) {
          delete statusListeners[transactionIdStr];
        }
      }
      
      // Stop polling
      stop();
    }
  };
};

/**
 * Get transaction history for a specific staff member
 * @param staffId The ID of the staff member
 * @param filters Optional filters for the transaction history
 * @returns The transaction history
 */
export const getStaffTransactionHistory = async (
  staffId: string,
  filters?: {
    startDate?: string;
    endDate?: string;
    status?: TransactionStatus['status'];
    type?: 'transfer' | 'payment';
    page?: number;
    limit?: number;
  }
): Promise<{ transactions: TransactionStatus[]; totalPages: number }> => {
  try {
    const queryParams = new URLSearchParams();
    
    if (filters?.startDate) {
      queryParams.append('startDate', filters.startDate);
    }
    
    if (filters?.endDate) {
      queryParams.append('endDate', filters.endDate);
    }
    
    if (filters?.status) {
      queryParams.append('status', filters.status);
    }
    
    if (filters?.type) {
      queryParams.append('type', filters.type);
    }
    
    if (filters?.page) {
      queryParams.append('page', filters.page.toString());
    }
    
    if (filters?.limit) {
      queryParams.append('limit', filters.limit.toString());
    }
    
    const response = await api.get(`/staff/${staffId}/transactions?${queryParams.toString()}`);
    return response.data.data;
  } catch (error) {
    console.error(`Error fetching transaction history for staff ${staffId}:`, error);
    throw error;
  }
};

/**
 * Retry a failed transaction
 * @param transactionId The ID of the transaction to retry
 * @returns The updated transaction status
 */
export const retryTransaction = async (transactionId: number | string): Promise<TransactionStatus> => {
  try {
    const response = await api.post(`/transactions/${transactionId}/retry`);
    
    const updatedStatus = response.data.data;
    
    // Notify all listeners about the status change
    notifyStatusChange(transactionId.toString(), updatedStatus);
    
    return updatedStatus;
  } catch (error) {
    console.error(`Error retrying transaction ${transactionId}:`, error);
    throw error;
  }
};