import api from './api';

export interface PaymentMethod {
  id: number;
  method_id: string;
  name: string;
  description: string;
  processing_time: string;
  fee: number;
  is_active: boolean;
  method_type: 'withdrawal' | 'deposit' | 'both';
  min_amount: number;
  max_amount: number | null;
  display_order: number;
  created_at: string;
  updated_at: string;
}

export interface WithdrawalStep {
  id: number;
  step_key: string;
  title: string;
  description: string;
  step_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface WithdrawalProgress {
  step_key: string;
  title: string;
  description: string;
  step_order: number;
  status: 'pending' | 'completed' | 'failed' | 'skipped';
  completed_at: string | null;
  notes: string | null;
}

/**
 * Get all payment methods
 */
export const getPaymentMethods = async (type?: 'withdrawal' | 'deposit' | 'both'): Promise<PaymentMethod[]> => {
  try {
    const params = type ? `?type=${type}` : '';
    const response = await api.get(`/payment-methods${params}`);

    if (response.data && response.data.success) {
      return response.data.data.payment_methods;
    } else {
      return [];
    }
  } catch (error: unknown) {
    console.error('Error fetching payment methods:', error);
    return [];
  }
};

/**
 * Get payment method by ID
 */
export const getPaymentMethodById = async (methodId: string): Promise<PaymentMethod | null> => {
  try {
    const response = await api.get(`/payment-methods/${methodId}`);

    if (response.data && response.data.success) {
      return response.data.data.payment_method;
    } else {
      return null;
    }
  } catch (error: unknown) {
    console.error('Error fetching payment method by ID:', error);
    return null;
  }
};

/**
 * Get withdrawal steps template
 */
export const getWithdrawalSteps = async (): Promise<WithdrawalStep[]> => {
  try {
    const response = await api.get('/withdrawal-steps');

    if (response.data && response.data.success) {
      return response.data.data.withdrawal_steps;
    } else {
      return [];
    }
  } catch (error: unknown) {
    console.error('Error fetching withdrawal steps:', error);
    return [];
  }
};

/**
 * Get withdrawal progress for a specific transaction
 */
export const getWithdrawalProgress = async (transactionId: number): Promise<{
  transaction: any;
  steps: WithdrawalProgress[];
} | null> => {
  try {
    const response = await api.get(`/withdrawal-progress/${transactionId}`);

    if (response.data && response.data.success) {
      return response.data.data;
    } else {
      return null;
    }
  } catch (error: unknown) {
    console.error('Error fetching withdrawal progress:', error);
    return null;
  }
};

/**
 * Get user's withdrawal progress history
 */
export const getUserWithdrawalProgress = async (limit: number = 10, offset: number = 0): Promise<WithdrawalProgress[]> => {
  try {
    const response = await api.get(`/user-withdrawal-progress?limit=${limit}&offset=${offset}`);

    if (response.data && response.data.success) {
      return response.data.data.withdrawals;
    } else {
      return [];
    }
  } catch (error: unknown) {
    console.error('Error fetching user withdrawal progress:', error);
    return [];
  }
};
