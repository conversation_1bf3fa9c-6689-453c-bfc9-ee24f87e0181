import api from './api';

// Enhanced Transaction Interface
export interface Transaction {
  id: string;
  transactionId: string;
  date: string;
  paidByReceivedFrom: string;
  paidTo: string;
  type: string;
  amount: number;
  status: 'Received' | 'Pending' | 'Refund' | 'Failed' | 'Withdrawal' | 'Paid';
  category?: 'add_money' | 'withdraw' | 'payment_sent' | 'payment_received' | 'refund' | 'transfer';
  reference?: string;
  description?: string;
  metadata?: Record<string, unknown>;
  createdAt?: string;
  updatedAt?: string;
}

// Wallet Transaction Interface (matching your database schema)
export interface WalletTransaction {
  id: number;
  user_id: number;
  type: string; // 'credit', 'debit', 'bank_transfer', 'wallet_transfer', etc.
  amount: number;
  reference_id: string;
  payment_provider: string;
  description: string;
  status_id: string; // '1' = success, '2' = pending, '3' = failed
  created_at: string;
  meta_data?: Record<string, unknown>;
}

// Wallet Ledger Interface
export interface WalletLedger {
  id: number;
  transaction_id: number;
  entry_type: 'debit' | 'credit';
  amount: number;
  balance_after: number;
  created_at: string;
}

// Withdrawal Progress Interface
export interface WithdrawalProgress {
  id: number;
  transaction_id: number;
  step_key: string;
  status: 'pending' | 'completed' | 'failed' | 'skipped';
  completed_at?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

// Transaction Filter Interface
export interface TransactionFilter {
  status?: string;
  search?: string;
  type?: string;
  category?: string;
  dateFrom?: string;
  dateTo?: string;
  amountMin?: number;
  amountMax?: number;
  limit?: number;
  offset?: number;
}

// Transaction Summary Interface
export interface TransactionSummary {
  totalPaymentReceived: number;
  pendingPayments: number;
  refundPayments: number;
  totalWithdrawals: number;
  totalDeposits: number;
  transactionCount: number;
}

export const mockTransactions: Transaction[] = [
  {
    id: '1',
    transactionId: 'TXN-928374560',
    date: '2025-05-21',
    paidByReceivedFrom: 'Team Warriors',
    paidTo: 'Xtreme Sports Org',
    type: 'Team Connect Fee',
    amount: 5000.00,
    status: 'Received',
  },
  {
    id: '2',
    transactionId: 'TXN-918274350',
    date: '2025-05-20',
    paidByReceivedFrom: 'GameZone Org',
    paidTo: 'Organizer Wallet',
    type: 'Sponsor Payment',
    amount: 1000.00,
    status: 'Pending',
  },
  {
    id: '3',
    transactionId: 'TXN-928374560',
    date: '2025-05-21',
    paidByReceivedFrom: 'Team Warriors',
    paidTo: 'Xtreme Sports Org',
    type: 'Team Connect Fee',
    amount: 5000.00,
    status: 'Refund',
  },
  {
    id: '4',
    transactionId: 'TXN-918274350',
    date: '2025-05-20',
    paidByReceivedFrom: 'GameZone Org',
    paidTo: 'Organizer Wallet',
    type: 'Sponsor Payment',
    amount: 1000.00,
    status: 'Pending',
  },
  {
    id: '5',
    transactionId: 'TXN-928374560',
    date: '2025-05-21',
    paidByReceivedFrom: 'Team Warriors',
    paidTo: 'Xtreme Sports Org',
    type: 'Team Connect Fee',
    amount: 5000.00,
    status: 'Received',
  },
  {
    id: '6',
    transactionId: 'TXN-918274350',
    date: '2025-05-20',
    paidByReceivedFrom: 'GameZone Org',
    paidTo: 'Organizer Wallet',
    type: 'Sponsor Payment',
    amount: 1000.00,
    status: 'Failed',
  },
  {
    id: '7',
    transactionId: 'TXN-928374560',
    date: '2025-05-21',
    paidByReceivedFrom: 'Team Warriors',
    paidTo: 'Xtreme Sports Org',
    type: 'Team Connect Fee',
    amount: 5000.00,
    status: 'Received',
  },
  {
    id: '8',
    transactionId: 'TXN-918274350',
    date: '2025-05-20',
    paidByReceivedFrom: 'GameZone Org',
    paidTo: 'Organizer Wallet',
    type: 'Sponsor Payment',
    amount: 1000.00,
    status: 'Refund',
  },
  {
    id: '9',
    transactionId: 'TXN-928374560',
    date: '2025-05-21',
    paidByReceivedFrom: 'Team Warriors',
    paidTo: 'Xtreme Sports Org',
    type: 'Team Connect Fee',
    amount: 5000.00,
    status: 'Withdrawal',
  },
  {
    id: '10',
    transactionId: 'TXN-918274350',
    date: '2025-05-20',
    paidByReceivedFrom: 'GameZone Org',
    paidTo: 'Organizer Wallet',
    type: 'Sponsor Payment',
    amount: 1000.00,
    status: 'Pending',
  },
];

// Real API Functions
// Backend transaction format (from the API response)
interface BackendTransaction {
  id: number;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  provider: string;
  reference: string;
  status: 'completed' | 'pending';
  date: string;
  metadata: Record<string, unknown>;
}

export const getWalletTransactions = async (filters: TransactionFilter = {}): Promise<{ data: BackendTransaction[], total: number }> => {
  try {
    const params = new URLSearchParams();

    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.offset) params.append('offset', filters.offset.toString());

    console.log('🔄 Fetching transactions from API:', `/wallet/transactions?${params.toString()}`);

    const response = await api.get(`/wallet/transactions?${params.toString()}`);

    console.log('✅ API Response:', response.data);

    if (response.data && response.data.success && response.data.data) {
      return {
        data: response.data.data.transactions || [],
        total: response.data.data.total || response.data.data.transactions?.length || 0
      };
    } else {
      console.warn('⚠️ Unexpected API response format:', response.data);
      return { data: [], total: 0 };
    }
  } catch (error) {
    console.error('❌ Error fetching wallet transactions:', error);
    throw error;
  }
};

// Get wallet balance
export const getWalletBalance = async (): Promise<number> => {
  try {
    const response = await api.get('/wallet/balance');
    return response.data.data.balance;
  } catch (error) {
    console.error('Error fetching wallet balance:', error);
    throw error;
  }
};

export const getTransactions = async (filters: TransactionFilter = {}): Promise<{ data: Transaction[], totalPages: number }> => {
  try {
    console.log('🔄 Getting transactions with filters:', filters);

    // Try to get real wallet transactions first
    const walletData = await getWalletTransactions(filters);

    console.log('📊 Raw wallet data:', walletData);

    if (!walletData.data || walletData.data.length === 0) {
      console.log('⚠️ No transactions found from API, returning empty state');
      // Return empty state instead of mock data
      return {
        data: [],
        totalPages: 0
      };
    }

    // Transform backend transactions to Transaction format
    const transformedTransactions: Transaction[] = walletData.data.map((tx: BackendTransaction) => {
      // Determine transaction category based on type and metadata
      let category: string;
      let paidByReceivedFrom: string;
      let paidTo: string;

      const metadata = tx.metadata || {};

      if (tx.type === 'credit') {
        if (tx.reference?.startsWith('BANK_') || tx.reference?.startsWith('sim_transfer_')) {
          category = 'add_money';
          paidByReceivedFrom = (metadata.bankName as string) || tx.provider;
          paidTo = 'Your Wallet';
        } else if (tx.reference?.startsWith('W2W_')) {
          category = 'payment_received';
          paidByReceivedFrom = (metadata.senderName as string) || 'Unknown User';
          paidTo = 'Your Wallet';
        } else {
          category = 'payment_received';
          paidByReceivedFrom = tx.provider;
          paidTo = 'Your Wallet';
        }
      } else if (tx.type === 'debit') {
        if (tx.reference?.startsWith('WITHDRAW_')) {
          category = 'withdraw';
          paidByReceivedFrom = 'Your Wallet';
          paidTo = (metadata.bankName as string) || tx.provider;
        } else if (tx.reference?.startsWith('W2W_')) {
          category = 'payment_sent';
          paidByReceivedFrom = 'Your Wallet';
          paidTo = (metadata.recipientName as string) || 'Unknown User';
        } else {
          category = 'payment_sent';
          paidByReceivedFrom = 'Your Wallet';
          paidTo = tx.provider;
        }
      } else {
        // Default case
        category = 'transfer';
        paidByReceivedFrom = tx.provider;
        paidTo = 'Your Wallet';
      }

      return {
        id: tx.id.toString(),
        transactionId: tx.reference || `TXN-${tx.id}`,
        date: tx.date,
        paidByReceivedFrom,
        paidTo,
        type: tx.description,
        amount: Math.abs(tx.amount),
        status: tx.status === 'completed' ? 'Received' : 'Pending',
        category: category as Transaction['category'],
        reference: tx.reference,
        description: tx.description,
        metadata: tx.metadata,
        createdAt: tx.date
      };
    });

    // Apply additional filters
    let filteredData = transformedTransactions;

    if (filters.status && filters.status !== 'All') {
      filteredData = filteredData.filter(t => t.status === filters.status);
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredData = filteredData.filter(t =>
        t.transactionId.toLowerCase().includes(searchTerm) ||
        t.paidByReceivedFrom.toLowerCase().includes(searchTerm) ||
        t.paidTo.toLowerCase().includes(searchTerm) ||
        t.type.toLowerCase().includes(searchTerm)
      );
    }

    if (filters.type && filters.type !== 'All') {
      filteredData = filteredData.filter(t => t.category === filters.type);
    }

    if (filters.dateFrom) {
      filteredData = filteredData.filter(t => new Date(t.date) >= new Date(filters.dateFrom!));
    }

    if (filters.dateTo) {
      filteredData = filteredData.filter(t => new Date(t.date) <= new Date(filters.dateTo!));
    }

    if (filters.amountMin !== undefined) {
      filteredData = filteredData.filter(t => t.amount >= filters.amountMin!);
    }

    if (filters.amountMax !== undefined) {
      filteredData = filteredData.filter(t => t.amount <= filters.amountMax!);
    }

    const totalPages = Math.ceil(filteredData.length / (filters.limit || 10));
    return { data: filteredData, totalPages };

  } catch (error) {
    console.error('❌ API Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      response: (error as Record<string, unknown>)?.response,
      status: (error as Record<string, unknown>)?.status,
      url: (error as Record<string, unknown>)?.config
    });

    console.warn('⚠️ API not available, returning empty state');

    // Return empty state instead of mock data
    return {
      data: [],
      totalPages: 0
    };
  }
};

export const getTransactionById = async (id: string): Promise<Transaction> => {
  try {
    // Try to get from wallet transactions first
    const walletData = await getWalletTransactions({ limit: 1000 }); // Get all to search
    const walletTransaction = walletData.data.find(tx => tx.id.toString() === id);

    if (walletTransaction) {
      return {
        id: walletTransaction.id.toString(),
        transactionId: walletTransaction.reference || `TXN-${walletTransaction.id}`,
        date: walletTransaction.date,
        paidByReceivedFrom: walletTransaction.type === 'credit' ? walletTransaction.provider : 'Your Wallet',
        paidTo: walletTransaction.type === 'debit' ? walletTransaction.provider : 'Your Wallet',
        type: walletTransaction.description,
        amount: Math.abs(walletTransaction.amount),
        status: walletTransaction.status === 'completed' ? 'Received' : 'Pending',
        category: walletTransaction.type === 'credit' ? 'add_money' : 'withdraw',
        reference: walletTransaction.reference,
        description: walletTransaction.description,
        metadata: walletTransaction.metadata,
        createdAt: walletTransaction.date
      };
    }

    throw new Error('Transaction not found');
  } catch (error) {
    console.warn('API not available:', error);
    throw new Error('Transaction not found');
  }
};

// Get transaction status details
export const getTransactionStatus = async (id: string): Promise<{
  status: string;
  details: Record<string, unknown>;
  timeline: WithdrawalProgress[];
}> => {
  try {
    // Check if it's a withdrawal transaction
    const transaction = await getTransactionById(id);

    if (transaction.category === 'withdraw') {
      // Get withdrawal progress from your database
      const response = await api.get(`/wallet/withdrawal-progress/${id}`);
      return {
        status: transaction.status,
        details: transaction.metadata || {},
        timeline: response.data.data || []
      };
    }

    // For other transaction types, create a simple timeline
    const timeline: WithdrawalProgress[] = [
      {
        id: 1,
        transaction_id: parseInt(id),
        step_key: 'completed',
        status: transaction.status === 'Received' ? 'completed' :
                transaction.status === 'Failed' ? 'failed' : 'pending',
        completed_at: transaction.status === 'Received' ? transaction.createdAt : undefined,
        notes: transaction.description,
        created_at: transaction.createdAt || new Date().toISOString(),
        updated_at: transaction.createdAt || new Date().toISOString()
      }
    ];

    return {
      status: transaction.status,
      details: transaction.metadata || {},
      timeline
    };
  } catch (error) {
    console.error('Error fetching transaction status:', error);
    throw error;
  }
};

// Export transactions to CSV
export const exportTransactionsToCSV = async (filters: TransactionFilter = {}): Promise<string> => {
  try {
    const { data: transactions } = await getTransactions({ ...filters, limit: 10000 });

    // Create CSV header
    const header = 'Transaction ID,Date,Time,Paid By/Received From,Paid To,Type,Category,Amount,Status,Reference,Description\n';

    // Create CSV rows
    const rows = transactions.map(t => {
      const date = new Date(t.date);
      const dateStr = date.toLocaleDateString();
      const timeStr = date.toLocaleTimeString();

      return [
        `"${t.transactionId}"`,
        `"${dateStr}"`,
        `"${timeStr}"`,
        `"${t.paidByReceivedFrom}"`,
        `"${t.paidTo}"`,
        `"${t.type}"`,
        `"${t.category || 'N/A'}"`,
        `"$${t.amount.toFixed(2)}"`,
        `"${t.status}"`,
        `"${t.reference || 'N/A'}"`,
        `"${(t.description || '').replace(/"/g, '""')}"`
      ].join(',');
    }).join('\n');

    return header + rows;
  } catch (error) {
    console.error('Error exporting transactions:', error);
    throw error;
  }
};

// Export transactions to JSON
export const exportTransactionsToJSON = async (filters: TransactionFilter = {}): Promise<string> => {
  try {
    const { data: transactions } = await getTransactions({ ...filters, limit: 10000 });

    const exportData = {
      exportDate: new Date().toISOString(),
      filters: filters,
      totalTransactions: transactions.length,
      transactions: transactions.map(t => ({
        id: t.id,
        transactionId: t.transactionId,
        date: t.date,
        paidByReceivedFrom: t.paidByReceivedFrom,
        paidTo: t.paidTo,
        type: t.type,
        category: t.category,
        amount: t.amount,
        status: t.status,
        reference: t.reference,
        description: t.description,
        metadata: t.metadata
      }))
    };

    return JSON.stringify(exportData, null, 2);
  } catch (error) {
    console.error('Error exporting transactions to JSON:', error);
    throw error;
  }
};

// Download file helper
export const downloadFile = (content: string, filename: string, contentType: string) => {
  const blob = new Blob([content], { type: contentType });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

// Export and download transactions
export const exportAndDownloadTransactions = async (
  format: 'csv' | 'json',
  filters: TransactionFilter = {}
): Promise<void> => {
  try {
    let content: string;
    let filename: string;
    let contentType: string;

    const timestamp = new Date().toISOString().split('T')[0];

    if (format === 'csv') {
      content = await exportTransactionsToCSV(filters);
      filename = `transactions_${timestamp}.csv`;
      contentType = 'text/csv;charset=utf-8;';
    } else {
      content = await exportTransactionsToJSON(filters);
      filename = `transactions_${timestamp}.json`;
      contentType = 'application/json;charset=utf-8;';
    }

    downloadFile(content, filename, contentType);
  } catch (error) {
    console.error('Error exporting and downloading transactions:', error);
    throw error;
  }
};

export const getTransactionSummary = async (): Promise<TransactionSummary> => {
  try {
    // Add artificial delay to show the engaging loader
    await new Promise(resolve => setTimeout(resolve, 1000));

    const { data: transactions } = await getTransactions({ limit: 1000 });

    const summary: TransactionSummary = {
      totalPaymentReceived: transactions
        .filter(t => t.status === 'Received' && t.category === 'payment_received')
        .reduce((sum, t) => sum + t.amount, 0),
      pendingPayments: transactions
        .filter(t => t.status === 'Pending')
        .reduce((sum, t) => sum + t.amount, 0),
      refundPayments: transactions
        .filter(t => t.status === 'Refund')
        .reduce((sum, t) => sum + t.amount, 0),
      totalWithdrawals: transactions
        .filter(t => t.category === 'withdraw')
        .reduce((sum, t) => sum + t.amount, 0),
      totalDeposits: transactions
        .filter(t => t.category === 'add_money')
        .reduce((sum, t) => sum + t.amount, 0),
      transactionCount: transactions.length
    };

    return summary;
  } catch (error) {
    console.warn('Error fetching transaction summary:', error);

    // Return empty summary instead of mock data
    return {
      totalPaymentReceived: 0,
      pendingPayments: 0,
      refundPayments: 0,
      totalWithdrawals: 0,
      totalDeposits: 0,
      transactionCount: 0
    };
  }
};

// Debug function to test API connectivity
export const debugTransactionAPI = async () => {
  console.log('🔍 Debug: Testing transaction API connectivity...');

  try {
    // Check if user is authenticated
    const token = localStorage.getItem('token');
    console.log('🔑 Auth token exists:', !!token);

    if (!token) {
      console.log('❌ No authentication token found');
      return { success: false, error: 'No authentication token' };
    }

    // Test basic API connectivity
    console.log('🌐 Testing API endpoint...');
    const response = await api.get('/wallet/transactions?limit=1');

    console.log('✅ API Response received:', {
      status: response.status,
      data: response.data
    });

    return { success: true, data: response.data };

  } catch (error) {
    console.error('❌ API Debug failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error
    };
  }
};