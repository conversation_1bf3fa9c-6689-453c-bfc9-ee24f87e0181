import { Star } from 'lucide-react';
import api from './api';

export interface AwaitingPayment {
  id: string;
  team_name:string;
  paid_amount:number;
  players_per_team:number;
  team_logo:string;
  payerName: string;
  expected_amount:number;
  email: string;
  phone: string;
  game_title: string;
  season_name: string;
  price: number;
  end_date: string;
  status: 'Pending' | 'Overdue' | 'Paid';
  avatar: string;
    users?:[]
}

export interface TeamPaymentDetail {
  team_id: number;
  team_name: string;
  team_logo: string;
  user_id: number | null;
  firstname: string | null;
  email: string | null;
  role_name: string | null;
  season_name: string;
  plan_date: string;
  final_day_of_payment: string;
  await_payment?: number;
  roles?: string[];
  users?:[]
}

// Add interface for filter modal options
export interface ModalFilterOption {
  game_id: number;
  game_title: string;
  season_id: number;
  season_name: string;
  game_group_id: number;
  group_name: string;
  status_id: number;
  created_at: string;
}

// Static data for development
export const mockAwaitingPayments: AwaitingPayment[] = [
  // {
  //   id: '1',
  //   payerName: '<PERSON><PERSON><PERSON>',
  //   team_name:'',
  //   team_logo:'',
  //   total_price:0,
  //   amount_paid:0,
  //   email: '<EMAIL>',
  //   phone: '0987654321',
  //   game_title: 'Summer League 2025',
  //   season_name: 'Summer',
  //   price: 1000,
  //   end_date: '2025-06-10',
  //   status: 'Pending',
  //   avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
  // },
  // {
  //   id: '2',
  //   payerName: 'Priya Sharma',
  //   email: '<EMAIL>',
  //      team_name:'',
  //   total_price:0,
  //   amount_paid:0,

  //   team_logo:'',
  //   phone: '9876543210',
  //   game_title: 'Winter League 2024',
  //   season_name: 'Winter',
  //   price: 1500,
  //   end_date: '2024-12-15',
  //   status: 'Overdue',
  //   avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
  // },
  // {
  //   id: '3',
  //   payerName: 'Amit Patel',
  //      team_name:'',
  //   team_logo:'',
  //   total_price:0,
  //   amount_paid:0,

  //   email: '<EMAIL>',
  //   phone: '8765432109',
  //   game_title: 'Spring League 2025',
  //   season_name: 'Spring',
  //   price: 2000,
  //   end_date: '2025-03-20',
  //   status: 'Pending',
  //   avatar: 'https://randomuser.me/api/portraits/men/3.jpg'
  // }
];

// Update getAwaitingPayments to accept filters, search, and pagination
export const getAwaitingPayments = async (params?: { 
  game_id?: string | null; 
  season_id?: string | null; 
  group_id?: string | null;
  search?: string;
  page?: number;
  pageSize?: number;
}) => {
  // When API is ready, uncomment this:
  const response = await api.post('/payments/awaiting-payment', params || {});
  return response.data.data;
};

export const getAwaitingPaymentById = async (id: string): Promise<TeamPaymentDetail> => {
  await new Promise(resolve => setTimeout(resolve, 800));
  const response = await api.post(`/payments/awaiting-team-payments/${id}`);

  console.log(response.data,'response.data')
  return response.data.data;
};

export const updatePaymentStatus = async (id: string, status: AwaitingPayment['status']): Promise<AwaitingPayment> => {
  // For development, return mock data
  const payment = mockAwaitingPayments.find(p => p.id === id);
  if (!payment) throw new Error('Payment not found');
  
  return Promise.resolve({
    ...payment,
    status
  });
  
  // When API is ready, uncomment this:
  // const response = await api.patch(`/awaiting-payments/${id}`, { status });
  // return response.data;
};
export const getAwaitingFiltersById = async (): Promise<ModalFilterOption[]> => {
  await new Promise(resolve => setTimeout(resolve, 800));
  const response = await api.get(`/payments/get-filters`);

  console.log(response.data,'response.data')
  return response.data.data;
};

export const getPaymentSummary = async () => {
  // Add artificial delay to show the engaging loader
  await new Promise(resolve => setTimeout(resolve, 1200));
  const payments = await getAwaitingPayments();

  return {
    totalIncoming: payments,
    overdue: 120,
    pending: 1
  };
};


export const sendReminder = async (userData: object): Promise<TeamPaymentDetail> => {
  await new Promise(resolve => setTimeout(resolve, 800));
  const response = await api.post(`/payments/send-reminder-email`,userData);

  console.log(response.data,'response.data')
  return response.data.data;
};