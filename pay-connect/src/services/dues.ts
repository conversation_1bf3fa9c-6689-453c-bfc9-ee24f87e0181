import api from './api';

// API Response interfaces for Platform Due details
interface PlatformDueTeamAPIResponse {
  id?: string;
  team_id?: string;
  name?: string;
  team_name?: string;
  captain?: string;
  captain_name?: string;
  players?: number;
  player_count?: number;
  amountDue?: number;
  amount_due?: number;
  amountPaid?: number;
  amount_paid?: number;
  status?: 'Paid' | 'Pending';
}

interface PlatformDuePaymentAPIResponse {
  id?: string;
  payment_id?: string;
  date?: string;
  payment_date?: string;
  created_at?: string;
  amount?: number;
  payment_amount?: number;
  method?: string;
  payment_method?: string;
  reference?: string;
  payment_reference?: string;
  transaction_id?: string;
  teams?: string[];
  team_names?: string[];
}

interface PlatformDueAPIResponse {
  id?: string;
  due_id?: string;
  season_name?: string;
  seasonName?: string;
  league_name?: string;
  leagueName?: string;
  division_name?: string;
  divisionName?: string;
  teams_count?: number;
  teamsCount?: number;
  total_amount?: number;
  totalAmount?: number;
  amount_pending?: number;
  amountPending?: number;
  amount_paid?: number;
  amountPaid?: number;
  due_date?: string;
  dueDate?: string;
  status?: 'Paid' | 'Pending' | 'Overdue';
  created_at?: string;
  createdAt?: string;
  updated_at?: string;
  updatedAt?: string;
}

// Enhanced Due interface to match backend data structure
export interface Due {
  id: string;
  firstname: string;
  lastname?: string;
  full_name?: string;
  email: string;
  contact?: string;
  avatar_url?: string;
  game_title: string;
  season_name: string;
  amount: number; // Legacy field for compatibility
  base_amount?: number;
  payconnect_fee?: number;
  tax_amount?: number;
  total_amount?: number;
  end_date: string;
  due_date?: string;
  status: 'Paid' | 'Pending' | 'Overdue';
  user_id?: string;
  team_id?: string;
  season_id?: string;
  game_id?: string;
  match_id?: string;
  organizer_id?: string;
  participation_type?: string;
  organizer_name?: string;
  game_logo?: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
  payment_date?: string;
  // Structured data from backend
  member_details?: {
    name: string;
    email: string;
    contact: string;
    avatar_url: string;
    participation_type: string;
  };
  league_info?: {
    name: string;
    logo: string;
    organizer: string;
    due_date: string;
    season_name: string;
  };
  payment_breakdown?: Array<{
    label: string;
    value: number;
  }>;
  // Legacy fields for compatibility
  avatarUrl?: string;
  group_id?: string;
  payment_method?: string;
  transaction_id?: string;
}

// Platform Due interface for organization dues
export interface PlatformDue {
  id: string;
  season_name: string;
  league_name: string;
  division_name?: string;
  teams_count: number;
  total_amount: number;
  amount_pending: number;
  amount_paid: number;
  due_date: string;
  status: 'Paid' | 'Pending' | 'Overdue';
  created_at: string;
  updated_at?: string;
}

// Payment request interface
export interface PaymentRequest {
  dueId: string;
  amount: number;
  paymentMethod: 'wallet' | 'bank';
  pin?: string;
  recipientUserId?: string;
}

// Payment response interface
export interface PaymentResponse {
  success: boolean;
  message: string;
  transactionId?: string;
  newBalance?: number;
  paymentDetails?: {
    amount: number;
    recipient: string;
    timestamp: string;
  };
}

// Dues filter interface
export interface DuesFilter {
  status?: string;
  search?: string;
  game_id?: string;
  season_id?: string;
  team_id?: string;
  limit?: number;
  offset?: number;
}

export const mockDues: Due[] = [

];

// Mock platform dues data
export const mockPlatformDues: PlatformDue[] = [
  {
    id: 'pd1',
    season_name: 'Spring 2025',
    league_name: 'Premier League',
    division_name: 'Division A',
    teams_count: 12,
    total_amount: 15000,
    amount_pending: 8500,
    amount_paid: 6500,
    due_date: '2025-04-30',
    status: 'Pending',
    created_at: '2025-01-15T10:00:00Z',
  },
  {
    id: 'pd2',
    season_name: 'Winter 2024',
    league_name: 'Championship League',
    division_name: 'Division B',
    teams_count: 8,
    total_amount: 10000,
    amount_pending: 0,
    amount_paid: 10000,
    due_date: '2024-12-31',
    status: 'Paid',
    created_at: '2024-10-01T10:00:00Z',
  },
  {
    id: 'pd3',
    season_name: 'Fall 2024',
    league_name: 'Youth League',
    division_name: 'Under 18',
    teams_count: 16,
    total_amount: 20000,
    amount_pending: 20000,
    amount_paid: 0,
    due_date: '2024-11-15',
    status: 'Overdue',
    created_at: '2024-08-01T10:00:00Z',
  },
  {
    id: 'pd4',
    season_name: 'Summer 2024',
    league_name: 'Amateur League',
    teams_count: 6,
    total_amount: 7500,
    amount_pending: 2500,
    amount_paid: 5000,
    due_date: '2024-08-31',
    status: 'Pending',
    created_at: '2024-06-01T10:00:00Z',
  },
  {
    id: 'pd5',
    season_name: 'Spring 2025',
    league_name: 'Professional Soccer League',
    division_name: 'First Division',
    teams_count: 20,
    total_amount: 35000,
    amount_pending: 12000,
    amount_paid: 23000,
    due_date: '2025-05-15',
    status: 'Pending',
    created_at: '2025-01-20T09:00:00Z',
  },
  {
    id: 'pd6',
    season_name: 'Winter 2024',
    league_name: 'Basketball Premier',
    division_name: 'Elite Division',
    teams_count: 10,
    total_amount: 18000,
    amount_pending: 0,
    amount_paid: 18000,
    due_date: '2024-12-20',
    status: 'Paid',
    created_at: '2024-09-15T11:00:00Z',
  },
  {
    id: 'pd7',
    season_name: 'Fall 2024',
    league_name: 'Cricket Championship',
    division_name: 'Premier Division',
    teams_count: 14,
    total_amount: 28000,
    amount_pending: 28000,
    amount_paid: 0,
    due_date: '2024-10-30',
    status: 'Overdue',
    created_at: '2024-07-10T14:00:00Z',
  },
  {
    id: 'pd8',
    season_name: 'Summer 2024',
    league_name: 'Tennis League',
    division_name: 'Open Division',
    teams_count: 4,
    total_amount: 5000,
    amount_pending: 1500,
    amount_paid: 3500,
    due_date: '2024-09-10',
    status: 'Pending',
    created_at: '2024-05-20T16:00:00Z',
  },
  {
    id: 'pd9',
    season_name: 'Spring 2025',
    league_name: 'Volleyball Championship',
    division_name: 'Women\'s Division',
    teams_count: 8,
    total_amount: 12000,
    amount_pending: 4000,
    amount_paid: 8000,
    due_date: '2025-06-01',
    status: 'Pending',
    created_at: '2025-02-01T12:00:00Z',
  },
  {
    id: 'pd10',
    season_name: 'Winter 2024',
    league_name: 'Hockey League',
    division_name: 'Junior Division',
    teams_count: 12,
    total_amount: 22000,
    amount_pending: 0,
    amount_paid: 22000,
    due_date: '2024-12-15',
    status: 'Paid',
    created_at: '2024-09-01T08:00:00Z',
  },
  {
    id: 'pd11',
    season_name: 'Fall 2024',
    league_name: 'Rugby Premier League',
    division_name: 'Championship',
    teams_count: 18,
    total_amount: 32000,
    amount_pending: 32000,
    amount_paid: 0,
    due_date: '2024-11-20',
    status: 'Overdue',
    created_at: '2024-08-15T10:00:00Z',
  },
  {
    id: 'pd12',
    season_name: 'Summer 2024',
    league_name: 'Swimming Championship',
    teams_count: 5,
    total_amount: 6500,
    amount_pending: 2000,
    amount_paid: 4500,
    due_date: '2024-08-25',
    status: 'Pending',
    created_at: '2024-05-10T13:00:00Z',
  },
  {
    id: 'pd13',
    season_name: 'Spring 2025',
    league_name: 'Badminton League',
    division_name: 'Mixed Doubles',
    teams_count: 16,
    total_amount: 24000,
    amount_pending: 9000,
    amount_paid: 15000,
    due_date: '2025-05-20',
    status: 'Pending',
    created_at: '2025-01-25T15:00:00Z',
  },
  {
    id: 'pd14',
    season_name: 'Winter 2024',
    league_name: 'Table Tennis Pro',
    division_name: 'Singles Division',
    teams_count: 24,
    total_amount: 30000,
    amount_pending: 0,
    amount_paid: 30000,
    due_date: '2024-12-10',
    status: 'Paid',
    created_at: '2024-09-20T07:00:00Z',
  },
  {
    id: 'pd15',
    season_name: 'Fall 2024',
    league_name: 'Golf Championship',
    division_name: 'Professional',
    teams_count: 30,
    total_amount: 45000,
    amount_pending: 45000,
    amount_paid: 0,
    due_date: '2024-10-15',
    status: 'Overdue',
    created_at: '2024-07-01T09:00:00Z',
  },
  {
    id: 'pd16',
    season_name: 'Summer 2024',
    league_name: 'Athletics Meet',
    division_name: 'Track & Field',
    teams_count: 25,
    total_amount: 37500,
    amount_pending: 12500,
    amount_paid: 25000,
    due_date: '2024-09-05',
    status: 'Pending',
    created_at: '2024-06-15T11:00:00Z',
  },
  {
    id: 'pd17',
    season_name: 'Spring 2025',
    league_name: 'Esports Championship',
    division_name: 'PC Gaming',
    teams_count: 32,
    total_amount: 48000,
    amount_pending: 16000,
    amount_paid: 32000,
    due_date: '2025-04-15',
    status: 'Pending',
    created_at: '2025-01-10T14:00:00Z',
  },
  {
    id: 'pd18',
    season_name: 'Winter 2024',
    league_name: 'Martial Arts League',
    division_name: 'Mixed Martial Arts',
    teams_count: 15,
    total_amount: 25000,
    amount_pending: 0,
    amount_paid: 25000,
    due_date: '2024-12-05',
    status: 'Paid',
    created_at: '2024-09-10T16:00:00Z',
  },
  {
    id: 'pd19',
    season_name: 'Fall 2024',
    league_name: 'Cycling Championship',
    division_name: 'Road Racing',
    teams_count: 22,
    total_amount: 33000,
    amount_pending: 33000,
    amount_paid: 0,
    due_date: '2024-11-10',
    status: 'Overdue',
    created_at: '2024-08-05T12:00:00Z',
  },
  {
    id: 'pd20',
    season_name: 'Summer 2024',
    league_name: 'Archery League',
    division_name: 'Recurve Division',
    teams_count: 8,
    total_amount: 12000,
    amount_pending: 3000,
    amount_paid: 9000,
    due_date: '2024-08-20',
    status: 'Pending',
    created_at: '2024-05-25T10:00:00Z',
  },
];

// Real API function to get dues
export const getDues = async (filters: DuesFilter = {}): Promise<{ data: Due[], totalPages: number }> => {
  try {
    console.log('🔄 Fetching dues from API with filters:', filters);

    // Call the new dues API endpoint
    const queryParams = new URLSearchParams();
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.limit) queryParams.append('limit', filters.limit.toString());
    if (filters.offset) queryParams.append('offset', filters.offset.toString());

    const response = await api.get(`/dues/staff-dues?${queryParams.toString()}`);

    console.log('✅ API Response:', response.data);

    if (response.data && response.data.success && response.data.data) {
      const dues: Due[] = response.data.data.data;
      const totalRecords = response.data.data.totalRecords;
      const pageSize = response.data.data.pageSize || 10;
      const totalPages = Math.ceil(totalRecords / pageSize);

      console.log(`📊 Processed ${dues.length} dues, ${totalPages} pages`);

      return { data: dues, totalPages };
    } else {
      console.warn('⚠️ Unexpected API response format:', response.data);
      return { data: [], totalPages: 0 };
    }
  } catch (error) {
    console.error('❌ Error fetching dues from API:', error);
    console.warn('⚠️ Falling back to mock data');

    // Fallback to mock data with filtering
    let filteredData = mockDues;

    if (filters.status && filters.status !== 'All') {
      filteredData = filteredData.filter(due => due.status === filters.status);
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredData = filteredData.filter(due =>
        due.firstname.toLowerCase().includes(searchTerm) ||
        due.email.toLowerCase().includes(searchTerm) ||
        due.game_title.toLowerCase().includes(searchTerm)
      );
    }

    const totalPages = Math.ceil(filteredData.length / (filters.limit || 10));
    return { data: filteredData, totalPages };
  }
};

export interface DuesSummary {
  totalDues: number;
  paidDues: number;
  pendingDues: number;
  overdueDues: number;
  totalAmountDue: number;
  totalAmountPaid?: number;
  recentPayments?: Due[];
}

export const getDuesSummary = async (): Promise<DuesSummary> => {
  try {
    console.log('📊 Fetching dues summary');

    // Use the staff dues summary API
    const response = await api.get('/dues/staff-dues-summary');

    console.log('✅ Summary API Response:', response.data);

    if (response.data && response.data.success) {
      const summary = response.data.data;

      return {
        totalDues: summary.totalDues || 0,
        paidDues: summary.paidDues || 0,
        pendingDues: summary.pendingDues || 0,
        overdueDues: summary.overdueDues || 0,
        totalAmountDue: summary.totalAmountDue || 0,
        totalAmountPaid: summary.totalAmountPaid || 0,
        recentPayments: summary.recentPayments || []
      };
    } else {
      console.warn('⚠️ Unexpected summary API response format:', response.data);
      return {
        totalDues: 0,
        paidDues: 0,
        pendingDues: 0,
        overdueDues: 0,
        totalAmountDue: 0,
        totalAmountPaid: 0,
        recentPayments: []
      };
    }
  } catch (error) {
    console.error('❌ Error fetching dues summary:', error);

    // Return empty summary in case of error
    return {
      totalDues: 0,
      paidDues: 0,
      pendingDues: 0,
      overdueDues: 0,
      totalAmountDue: 0,
      totalAmountPaid: 0
    };
  }
};

export const deleteDue = async (id: string): Promise<void> => {
  // For development, simulate deletion
  const initialLength = mockDues.length;
  mockDues.splice(mockDues.findIndex(due => due.id === id), 1);
  if (mockDues.length === initialLength) {
    throw new Error('Due not found');
  }
  return Promise.resolve();

  // When API is ready, uncomment this:
  // await api.delete(`/dues/${id}`);
};

export const getDueById = async (id: string): Promise<Due> => {
  try {
    console.log('🔍 Fetching due details for ID:', id);

    // Use the real API endpoint
    const response = await api.get(`/dues/get-due/${id}`);

    if (response.data && response.data.success) {
      const dueData = response.data.data;
      console.log('✅ Due details fetched successfully:', dueData);
      return dueData;
    } else {
      throw new Error('Failed to fetch due details');
    }
  } catch (error) {
    console.error('❌ Error fetching due details:', error);

    // Fallback to mock data for development
    console.log('🔄 Falling back to mock data for due ID:', id);
    const due = mockDues.find(d => d.id === id);
    if (!due) throw new Error('Due not found');
    return due;
  }
};

export const updateDueStatus = async (id: string, status: Due['status']): Promise<Due> => {
  // For development, update mock data
  const dueIndex = mockDues.findIndex(d => d.id === id);
  if (dueIndex === -1) throw new Error('Due not found');
  
  mockDues[dueIndex] = { ...mockDues[dueIndex], status };
  return Promise.resolve(mockDues[dueIndex]);

  // When API is ready, uncomment this:
  // const response = await api.patch(`/dues/${id}`, { status });
  // return response.data;
};

export const markDueAsPaid = async (id: string): Promise<Due> => {
  return updateDueStatus(id, 'Paid');
};

// Pay due with wallet transfer
export const payDueWithWallet = async (
  dueId: string,
  recipientUserId: string,
  amount: number,
  pin: string,
  description?: string
): Promise<PaymentResponse> => {
  try {
    console.log('💰 Initiating wallet payment for due:', { dueId, recipientUserId, amount });

    // Use the new payment API endpoint
    const response = await api.post('/payments/pay-due', {
      dueId,
      amount,
      recipientUserId,
      pin,
      description: description || `Payment for due ${dueId}`
    });

    console.log('✅ Payment API Response:', response.data);

    if (response.data && response.data.success) {
      const result = response.data.data;

      return {
        success: true,
        message: result.message || 'Payment completed successfully',
        transactionId: result.transactionId,
        newBalance: result.newBalance,
        paymentDetails: result.paymentDetails
      };
    } else {
      return {
        success: false,
        message: response.data?.message || 'Payment failed'
      };
    }
  } catch (error) {
    console.error('❌ Error paying due with wallet:', error);

    // Handle specific error responses
    if (error instanceof Error && 'response' in error) {
      const errorResponse = (error as Record<string, unknown>).response as Record<string, unknown>;
      if (errorResponse?.data && typeof errorResponse.data === 'object') {
        const errorData = errorResponse.data as Record<string, unknown>;
        if (errorData.message && typeof errorData.message === 'string') {
          return {
            success: false,
            message: errorData.message
          };
        }
      }
    }

    return {
      success: false,
      message: 'Payment failed. Please try again.'
    };
  }
};

// Pay multiple dues with wallet (bulk payment)
export const payMultipleDuesWithWallet = async (
  payments: Array<{
    dueId: string;
    recipientUserId: string;
    amount: number;
    description?: string;
  }>,
  pin: string
): Promise<{
  success: boolean;
  results: Array<{ dueId: string; success: boolean; message: string; transactionId?: string }>;
  totalAmount: number;
  successfulPayments: number;
  failedPayments: number;
}> => {
  try {
    console.log('💰 Initiating bulk wallet payments:', payments.length, 'payments');

    // Process payments individually using the payment API
    const results: Array<{ dueId: string; success: boolean; message: string; transactionId?: string }> = [];
    let successfulPayments = 0;
    let failedPayments = 0;
    const totalAmount = payments.reduce((sum, payment) => sum + payment.amount, 0);

    // Process payments sequentially to avoid race conditions
    for (const payment of payments) {
      try {
        const result = await payDueWithWallet(
          payment.dueId,
          payment.recipientUserId,
          payment.amount,
          pin,
          payment.description
        );

        results.push({
          dueId: payment.dueId,
          success: result.success,
          message: result.message,
          transactionId: result.transactionId
        });

        if (result.success) {
          successfulPayments++;
        } else {
          failedPayments++;
        }
      } catch (paymentError) {
        console.error('❌ Individual payment failed:', paymentError);
        results.push({
          dueId: payment.dueId,
          success: false,
          message: 'Payment failed'
        });
        failedPayments++;
      }
    }

    return {
      success: successfulPayments > 0,
      results,
      totalAmount,
      successfulPayments,
      failedPayments
    };
  } catch (error) {
    console.error('❌ Error processing bulk payments:', error);

    // Handle specific error responses
    if (error instanceof Error && 'response' in error) {
      const errorResponse = (error as Record<string, unknown>).response as Record<string, unknown>;
      if (errorResponse?.data && typeof errorResponse.data === 'object') {
        const errorData = errorResponse.data as Record<string, unknown>;
        if (errorData.message && typeof errorData.message === 'string') {
          console.error('Bulk payment API error:', errorData.message);
        }
      }
    }

    return {
      success: false,
      results: [],
      totalAmount: 0,
      successfulPayments: 0,
      failedPayments: payments.length
    };
  }
};

// Real-time status update functions
export const refreshDueStatus = async (dueId: string): Promise<Due | null> => {
  try {
    console.log('🔄 Refreshing status for due:', dueId);

    // Get updated due information
    const due = await getDueById(dueId);

    console.log('✅ Due status refreshed:', due);
    return due;
  } catch (error) {
    console.error('❌ Error refreshing due status:', error);
    return null;
  }
};

// Check payment status for a specific due
export const checkPaymentStatus = async (dueId: string, transactionId?: string): Promise<{
  status: 'pending' | 'completed' | 'failed';
  due?: Due;
  message?: string;
}> => {
  try {
    console.log('🔍 Checking payment status:', { dueId, transactionId });

    // Refresh the due status
    const updatedDue = await refreshDueStatus(dueId);

    if (!updatedDue) {
      return {
        status: 'failed',
        message: 'Could not retrieve due information'
      };
    }

    // Check if payment is completed
    if (updatedDue.status === 'Paid') {
      return {
        status: 'completed',
        due: updatedDue,
        message: 'Payment completed successfully'
      };
    } else if (updatedDue.status === 'Pending') {
      return {
        status: 'pending',
        due: updatedDue,
        message: 'Payment is being processed'
      };
    } else {
      return {
        status: 'failed',
        due: updatedDue,
        message: 'Payment failed or was not completed'
      };
    }
  } catch (error) {
    console.error('❌ Error checking payment status:', error);
    return {
      status: 'failed',
      message: 'Error checking payment status'
    };
  }
};

// Real-time dues monitoring (for dashboard updates)
export const startDuesMonitoring = (
  onDuesUpdate: (dues: Due[]) => void,
  intervalMs: number = 30000 // 30 seconds
): (() => void) => {
  console.log('🔄 Starting dues monitoring');

  const interval = setInterval(async () => {
    try {
      const { data: dues } = await getDues({});
      onDuesUpdate(dues);
    } catch (error) {
      console.error('❌ Error in dues monitoring:', error);
    }
  }, intervalMs);

  // Return cleanup function
  return () => {
    console.log('⏹️ Stopping dues monitoring');
    clearInterval(interval);
  };
};

// Notification system for due status changes
export interface DueNotification {
  id: string;
  type: 'payment_success' | 'payment_failed' | 'due_reminder' | 'due_overdue';
  title: string;
  message: string;
  dueId: string;
  timestamp: string;
  read: boolean;
}

export const createDueNotification = (
  type: DueNotification['type'],
  due: Due,
  additionalMessage?: string
): DueNotification => {
  const notifications = {
    payment_success: {
      title: 'Payment Successful',
      message: `Your payment of $${due.amount} for ${due.game_title} has been processed successfully.`
    },
    payment_failed: {
      title: 'Payment Failed',
      message: `Your payment of $${due.amount} for ${due.game_title} could not be processed. ${additionalMessage || 'Please try again.'}`
    },
    due_reminder: {
      title: 'Payment Reminder',
      message: `Your payment of $${due.amount} for ${due.game_title} is due on ${due.end_date}.`
    },
    due_overdue: {
      title: 'Payment Overdue',
      message: `Your payment of $${due.amount} for ${due.game_title} is now overdue. Please pay as soon as possible.`
    }
  };

  return {
    id: `notif_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
    type,
    title: notifications[type].title,
    message: notifications[type].message,
    dueId: due.id,
    timestamp: new Date().toISOString(),
    read: false
  };
};

// Platform Dues Functions
export const getPlatformDues = async (filters: { status?: string, search?: string, limit?: number, offset?: number }): Promise<{ data: PlatformDue[], totalPages: number }> => {
  try {
    console.log('🔄 Fetching platform dues from API with filters:', filters);

    // Call the platform dues API endpoint
    const queryParams = new URLSearchParams();
    if (filters.status && filters.status !== 'All') queryParams.append('status', filters.status);
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.limit) queryParams.append('limit', filters.limit.toString());
    if (filters.offset) queryParams.append('offset', filters.offset.toString());

    const response = await api.get(`/dues/platform-dues?${queryParams.toString()}`);

    console.log('✅ Platform Dues API Response:', response.data);

    if (response.data && response.data.success && response.data.data) {
      const apiData = response.data.data;
      const mappedData = (apiData.data || []).map((item: PlatformDueAPIResponse) => ({
        id: item.id || item.due_id || '',
        season_name: item.season_name || item.seasonName || '',
        league_name: item.league_name || item.leagueName || '',
        division_name: item.division_name || item.divisionName || null,
        teams_count: item.teams_count || item.teamsCount || 0,
        total_amount: item.total_amount || item.totalAmount || 0,
        amount_pending: item.amount_pending || item.amountPending || 0,
        amount_paid: item.amount_paid || item.amountPaid || 0,
        due_date: item.due_date || item.dueDate || '',
        status: item.status || 'Pending',
        created_at: item.created_at || item.createdAt || '',
        updated_at: item.updated_at || item.updatedAt || null
      }));

      return {
        data: mappedData,
        totalPages: Math.ceil((apiData.totalRecords || 0) / (apiData.pageSize || 20))
      };
    } else {
      console.warn('⚠️ Unexpected API response format:', response.data);
      return { data: [], totalPages: 0 };
    }
  } catch (error) {
    console.error('❌ Error fetching platform dues:', error);

    // Fallback to mock data for development
    console.log('🔄 Falling back to mock data for platform dues');
    let filteredData = mockPlatformDues;

    if (filters.status && filters.status !== 'All') {
      filteredData = filteredData.filter(due => due.status === filters.status);
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredData = filteredData.filter(due =>
        due.season_name.toLowerCase().includes(searchTerm) ||
        due.league_name.toLowerCase().includes(searchTerm) ||
        (due.division_name && due.division_name.toLowerCase().includes(searchTerm))
      );
    }

    const totalPages = Math.ceil(filteredData.length / (filters.limit || 10));
    return { data: filteredData, totalPages };
  }
};

export const getPlatformDueById = async (id: string): Promise<PlatformDue> => {
  try {
    console.log('🔍 Fetching platform due details for ID:', id);

    // Use the real API endpoint
    const response = await api.get(`/dues/platform-due/${id}`);

    if (response.data && response.data.success) {
      const dueData = response.data.data;
      console.log('✅ Platform due details fetched successfully:', dueData);
      return dueData;
    } else {
      throw new Error('Failed to fetch platform due details');
    }
  } catch (error) {
    console.error('❌ Error fetching platform due details:', error);

    // Fallback to mock data for development
    console.log('🔄 Falling back to mock data for platform due ID:', id);
    const due = mockPlatformDues.find(d => d.id === id);
    if (!due) throw new Error('Platform due not found');
    return due;
  }
};

export const getPlatformDuesSummary = async () => {
  try {
    console.log('📊 Fetching platform dues summary');

    // Use the platform dues summary API
    const response = await api.get('/dues/platform-dues-summary');

    console.log('✅ Platform Summary API Response:', response.data);

    if (response.data && response.data.success) {
      const summary = response.data.data;

      return {
        totalDues: summary.totalDues || 0,
        paidDues: summary.paidDues || 0,
        pendingDues: summary.pendingDues || 0,
        overdueDues: summary.overdueDues || 0,
        totalAmountDue: summary.totalAmountDue || 0,
        totalAmountPaid: summary.totalAmountPaid || 0,
        totalTeams: summary.totalTeams || 0
      };
    } else {
      throw new Error('Failed to fetch platform dues summary');
    }
  } catch (error) {
    console.error('❌ Error fetching platform dues summary:', error);

    // Fallback to mock data calculation
    console.log('🔄 Falling back to mock data calculation for platform summary');
    const { data: dues } = await getPlatformDues({});

    return {
      totalDues: dues.length,
      paidDues: dues.filter(due => due.status === 'Paid').length,
      pendingDues: dues.filter(due => due.status === 'Pending').length,
      overdueDues: dues.filter(due => due.status === 'Overdue').length,
      totalAmountDue: dues.filter(due => due.status !== 'Paid').reduce((sum, due) => sum + due.amount_pending, 0),
      totalAmountPaid: dues.reduce((sum, due) => sum + due.amount_paid, 0),
      totalTeams: dues.reduce((sum, due) => sum + due.teams_count, 0)
    };
  }
};

// Get teams for a platform due
export const getPlatformDueTeams = async (dueId: string): Promise<Array<{
  id: string;
  name: string;
  captain: string;
  players: number;
  amountDue: number;
  amountPaid: number;
  status: 'Paid' | 'Pending';
}>> => {
  try {
    console.log('🔍 Fetching teams for platform due:', dueId);

    const response = await api.get(`/dues/platform-due/${dueId}/teams`);

    console.log('✅ Platform Due Teams API Response:', response.data);

    if (response.data && response.data.success && response.data.data) {
      const teams = response.data.data.map((team: PlatformDueTeamAPIResponse) => ({
        id: team.id || team.team_id || '',
        name: team.name || team.team_name || '',
        captain: team.captain || team.captain_name || '',
        players: team.players || team.player_count || 0,
        amountDue: team.amountDue || team.amount_due || 0,
        amountPaid: team.amountPaid || team.amount_paid || 0,
        status: team.status || 'Pending'
      }));
      return teams;
    } else {
      throw new Error('Failed to fetch teams');
    }
  } catch (error) {
    console.error('❌ Error fetching teams:', error);

    // Fallback to mock data generation
    console.log('🔄 Generating mock teams data');
    const due = mockPlatformDues.find(d => d.id === dueId);
    if (!due) throw new Error('Platform due not found');

    return Array.from({ length: due.teams_count }, (_, i) => ({
      id: `team_${i + 1}`,
      name: `Team ${String.fromCharCode(65 + i)}`,
      captain: `Captain ${i + 1}`,
      players: Math.floor(Math.random() * 10) + 15,
      amountDue: Math.floor(due.total_amount / due.teams_count),
      amountPaid: Math.random() > 0.3 ? Math.floor(due.total_amount / due.teams_count) : 0,
      status: Math.random() > 0.3 ? 'Paid' : 'Pending',
    }));
  }
};

// Get payment history for a platform due
export const getPlatformDuePayments = async (dueId: string): Promise<Array<{
  id: string;
  date: string;
  amount: number;
  method: string;
  reference: string;
  teams: string[];
}>> => {
  try {
    console.log('🔍 Fetching payment history for platform due:', dueId);

    const response = await api.get(`/dues/platform-due/${dueId}/payments`);

    console.log('✅ Platform Due Payments API Response:', response.data);

    if (response.data && response.data.success && response.data.data) {
      const payments = response.data.data.map((payment: PlatformDuePaymentAPIResponse) => ({
        id: payment.id || payment.payment_id || '',
        date: payment.date || payment.payment_date || payment.created_at || '',
        amount: payment.amount || payment.payment_amount || 0,
        method: payment.method || payment.payment_method || 'Unknown',
        reference: payment.reference || payment.payment_reference || payment.transaction_id || '',
        teams: payment.teams || payment.team_names || []
      }));
      return payments;
    } else {
      throw new Error('Failed to fetch payment history');
    }
  } catch (error) {
    console.error('❌ Error fetching payment history:', error);

    // Fallback to mock data
    console.log('🔄 Generating mock payment history');
    return [
      {
        id: 'pay_1',
        date: '2024-12-15',
        amount: 5000,
        method: 'Bank Transfer',
        reference: 'TXN123456',
        teams: ['Team A', 'Team B', 'Team C'],
      },
      {
        id: 'pay_2',
        date: '2024-12-10',
        amount: 3000,
        method: 'Credit Card',
        reference: 'TXN123457',
        teams: ['Team D', 'Team E'],
      },
    ];
  }
};

// Send payment reminder for platform due
export const sendPlatformDueReminder = async (dueId: string): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('📧 Sending payment reminder for platform due:', dueId);

    const response = await api.post(`/dues/platform-due/${dueId}/send-reminder`);

    if (response.data && response.data.success) {
      console.log('✅ Reminder sent successfully');
      return {
        success: true,
        message: 'Payment reminder sent successfully'
      };
    } else {
      throw new Error('Failed to send reminder');
    }
  } catch (error) {
    console.error('❌ Error sending reminder:', error);

    // Simulate success for development
    return {
      success: true,
      message: 'Payment reminder sent successfully (simulated)'
    };
  }
};

// Update platform due status
export const updatePlatformDueStatus = async (dueId: string, status: 'Paid' | 'Pending' | 'Overdue'): Promise<boolean> => {
  try {
    console.log(`🔄 Updating platform due ${dueId} status to ${status}`);

    const response = await api.patch(`/dues/platform-due/${dueId}/status`, { status });

    console.log('✅ Platform Due Status Update Response:', response.data);

    if (response.data && response.data.success) {
      return true;
    } else {
      throw new Error('Failed to update platform due status');
    }
  } catch (error) {
    console.error('❌ Error updating platform due status:', error);
    return false;
  }
};