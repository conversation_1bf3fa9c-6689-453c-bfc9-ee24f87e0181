import React, { useState, useEffect, useCallback } from 'react';
import { Transaction } from '../../services/transactions';
import { ChevronLeft, ChevronRight, Filter, Download } from 'lucide-react';
import EmptyState from '../common/EmptyState';
import { getStaffTransactions, StaffTransactionFilters, StaffTransactionResponse } from '../../services/staffDetails';
import PaymentLoader from '../common/PaymentLoader';

interface StaffTransactionsTableProps {
  staffId: string;
  className?: string;
  searchTerm?: string;
  statusFilter?: string;
}

const StaffTransactionsTable: React.FC<StaffTransactionsTableProps> = ({
  staffId,
  className,
  searchTerm = '',
  statusFilter = 'All',
}) => {
  const [transactionData, setTransactionData] = useState<StaffTransactionResponse>({
    transactions: [],
    pagination: {
      total: 0,
      limit: 20,
      offset: 0,
      hasMore: false,
      currentPage: 1,
      totalPages: 0
    },
    filters: {}
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Advanced filters state
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [typeFilter, setTypeFilter] = useState('all');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');

  const itemsPerPage = 20;

  // Fetch transactions when component mounts or filters change
  useEffect(() => {
    if (staffId && staffId !== 'undefined') {
      fetchTransactions();
    } else {
      console.error('StaffTransactionsTable: staffId is undefined or invalid:', staffId);
    }
  }, [staffId, searchTerm, statusFilter, typeFilter, dateFrom, dateTo, transactionData.pagination.offset]);

  const fetchTransactions = useCallback(async () => {
    if (!staffId || staffId === 'undefined') {
      setError('Invalid staff ID');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('Fetching transactions for staffId:', staffId);

      const filters: StaffTransactionFilters = {
        limit: itemsPerPage,
        offset: transactionData.pagination.offset,
        search: searchTerm,
        type: typeFilter === 'all' ? '' : typeFilter,
        status: statusFilter === 'All' ? '' : statusFilter.toLowerCase(),
        dateFrom: dateFrom || undefined,
        dateTo: dateTo || undefined
      };

      const response = await getStaffTransactions(staffId, filters);
      setTransactionData(response);
    } catch (err) {
      setError('Failed to fetch transactions');
      console.error('Error fetching staff transactions:', err);
    } finally {
      setLoading(false);
    }
  }, [staffId, searchTerm, statusFilter, typeFilter, dateFrom, dateTo, transactionData.pagination.offset, itemsPerPage]);

  const handlePageChange = (newPage: number) => {
    const newOffset = (newPage - 1) * itemsPerPage;
    setTransactionData(prev => ({
      ...prev,
      pagination: {
        ...prev.pagination,
        offset: newOffset,
        currentPage: newPage
      }
    }));
  };

  const handleExport = () => {
    // Create CSV content
    const headers = ['Transaction ID', 'Date', 'From', 'To', 'Type', 'Amount', 'Status'];
    const csvContent = [
      headers.join(','),
      ...transactionData.transactions.map(t => [
        t.transactionId,
        t.date,
        `"${t.paidByReceivedFrom}"`,
        `"${t.paidTo}"`,
        `"${t.type}"`,
        t.amount,
        t.status
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `staff-${staffId}-transactions.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const clearFilters = () => {
    setTypeFilter('all');
    setDateFrom('');
    setDateTo('');
    setTransactionData(prev => ({
      ...prev,
      pagination: {
        ...prev.pagination,
        offset: 0,
        currentPage: 1
      }
    }));
  };

  const getStatusColor = (status: Transaction['status']) => {
    switch (status) {
      case 'Received':
      case 'Paid':
        return 'text-green-600';
      case 'Pending':
        return 'text-yellow-600';
      case 'Refund':
        return 'text-blue-600';
      case 'Failed':
        return 'text-red-600';
      case 'Withdrawal':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className={`rounded-md border border-gray-200 ${className}`}>
        <div className="flex items-center justify-center [min-height:765px] ">
        <PaymentLoader
          type="setup"
          message="Loading staff management dashboard..."
          size="large"
          showQuotes={true}
        />
      </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`rounded-md border border-gray-200 ${className}`}>
        <div className="flex items-center justify-center py-12">
          <div className="text-red-600 text-center">
            <p className="font-medium">Error loading transactions</p>
            <p className="text-sm mt-1">{error}</p>
            <button
              onClick={fetchTransactions}
              className="mt-2 px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Check if we should show empty state
  const showEmptyState = transactionData.transactions.length === 0;

  if (showEmptyState) {
    return (
      <div className={`rounded-md border border-gray-200 ${className}`}>
        <div className="p-6">
          {/* Advanced Filters */}
          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                
                <span>Advanced Filters</span>
                <Filter size={16} />
              </button>
              <button
                onClick={handleExport}
                className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
              
                <span>Export</span>
                  <Download size={16} />
              </button>
            </div>
            <div className="text-sm text-gray-500">
              Total: {transactionData.pagination.total} transactions
            </div>
          </div>

          {/* Advanced Filters Panel */}
          {showAdvancedFilters && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Transaction Type
                  </label>
                  <select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Types</option>
                    <option value="wallet_transfer">Wallet Transfer</option>
                    <option value="bank_transfer">Bank Transfer</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    From Date
                  </label>
                  <input
                    type="date"
                    value={dateFrom}
                    onChange={(e) => setDateFrom(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    To Date
                  </label>
                  <input
                    type="date"
                    value={dateTo}
                    onChange={(e) => setDateTo(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              <div className="mt-4 flex space-x-2">
                <button
                  onClick={clearFilters}
                  className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          )}
        </div>

        <EmptyState
          type="transactions"
          title="No transactions found"
          description="This staff member has no transaction history yet, or no transactions match your current filters."
          onAction={clearFilters}
        />
        </div>
    );
  }

  return (
    <div className={`rounded-md border border-gray-200 ${className}`}>
      <div className="p-6">
        {/* Advanced Filters */}
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
           
              <span>Advanced Filters</span>
                 <Filter size={16} />
            </button>
            <button
              onClick={handleExport}
              className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              
              <span>Export</span>
              <Download size={16} />
            </button>
          </div>
          <div className="text-sm text-gray-500">
            Showing {transactionData.transactions.length} of {transactionData.pagination.total} transactions
          </div>
        </div>

        {/* Advanced Filters Panel */}
        {showAdvancedFilters && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Transaction Type
                </label>
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Types</option>
                  <option value="wallet_transfer">Wallet Transfer</option>
                  <option value="bank_transfer">Bank Transfer</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  From Date
                </label>
                <input
                  type="date"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  To Date
                </label>
                <input
                  type="date"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="mt-4 flex space-x-2">
              <button
                onClick={clearFilters}
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        )}
      </div>

      <div className="overflow-x-auto border-1 border-gray-200 roundered-[30px]">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-[#404040] ">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                <input type="checkbox" className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out" />
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Transaction ID
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Date
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Event
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Season
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Role
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Amount
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {transactionData.transactions.map((transaction) => (
              <tr key={transaction.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <input type="checkbox" className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out" />
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {transaction.transactionId}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {new Date(transaction.date).toLocaleDateString('en-US', { day: 'numeric', month: 'short', year: 'numeric' })}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {transaction.paidByReceivedFrom}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {transaction.paidTo}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {transaction.type}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ${transaction.amount.toFixed(2)} {/* Assuming available balance is same as amount for now */}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <span className={getStatusColor(transaction.status)}>
                    {transaction.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <span className="text-gray-400 text-sm">No actions</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {/* Pagination */}
      {transactionData.pagination.totalPages > 1 && (
        <nav
          className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
          aria-label="Pagination"
        >
          <div className="flex-1 flex justify-between sm:justify-end items-center">
            <p className="text-sm text-gray-700 mr-4">
              Showing <span className="font-medium">{transactionData.pagination.offset + 1}</span> to{' '}
              <span className="font-medium">{Math.min(transactionData.pagination.offset + transactionData.pagination.limit, transactionData.pagination.total)}</span> of{' '}
              <span className="font-medium">{transactionData.pagination.total}</span> results
            </p>
            <div className="flex">
              <button
                onClick={() => handlePageChange(Math.max(1, transactionData.pagination.currentPage - 1))}
                disabled={transactionData.pagination.currentPage === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Previous</span>
                <ChevronLeft className="h-5 w-5" aria-hidden="true" />
              </button>
              <button
                onClick={() => handlePageChange(Math.min(transactionData.pagination.totalPages, transactionData.pagination.currentPage + 1))}
                disabled={transactionData.pagination.currentPage === transactionData.pagination.totalPages}
                className="ml-3 relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Next</span>
                <ChevronRight className="h-5 w-5" aria-hidden="true" />
              </button>
            </div>
          </div>
        </nav>
      )}
    </div>
  );
};

export default StaffTransactionsTable;
