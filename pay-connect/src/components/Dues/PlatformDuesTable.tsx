import React, { useState, useEffect } from 'react';
import { getPlatformDues, PlatformDue } from '../../services/dues';
import { ChevronLeft, ChevronRight, Eye, Users, Calendar, Trophy } from 'lucide-react';
import EmptyState from '../common/EmptyState';
import PlatformDueDetail from './PlatformDueDetail';

interface PlatformDuesTableProps {
  className?: string;
  search: string;
  statusFilter: string;
  onDataChange: () => void; // Callback to re-fetch summary when data changes
}

const PlatformDuesTable: React.FC<PlatformDuesTableProps> = ({
  className,
  search,
  statusFilter,
  onDataChange,
}) => {
  const [dues, setDues] = useState<PlatformDue[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedDue, setSelectedDue] = useState<PlatformDue | null>(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);

  useEffect(() => {
    fetchDues();
  }, [search, statusFilter, currentPage]);

  const fetchDues = async () => {
    try {
      setLoading(true);
      const response = await getPlatformDues({ status: statusFilter, search });
      setDues(response.data);
      setTotalPages(response.totalPages);
      setError(null);
    } catch (err) {
      setError('Failed to fetch platform dues');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: PlatformDue['status']) => {
    switch (status) {
      case 'Paid':
        return 'text-green-600 bg-green-100';
      case 'Pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'Overdue':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const handleViewDetails = (due: PlatformDue) => {
    setSelectedDue(due);
    setIsDetailOpen(true);
  };

  const handleCloseDetail = () => {
    setIsDetailOpen(false);
    setSelectedDue(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={fetchDues}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  if (dues.length === 0) {
    return (
      <EmptyState
        icon={Trophy}
        title="No Platform Dues Found"
        description="No platform dues match your current filters."
      />
    );
  }

  return (
    <>
      <div className={className}>
        {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-[#404040]">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Season & League
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Division
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Teams
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Total Amount
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Amount Pending
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Due Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {dues.map((due) => (
              <tr key={due.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                      <Trophy size={20} className="text-white" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">{due.season_name}</div>
                      <div className="text-sm text-gray-500">{due.league_name}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {due.division_name || 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-1">
                    <Users size={16} className="text-gray-400" />
                    <span className="text-sm font-medium text-gray-900">{due.teams_count || 0}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  ${(due.total_amount || 0).toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                  ${(due.amount_pending || 0).toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-1">
                    <Calendar size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-900">
                      {due.due_date ? new Date(due.due_date).toLocaleDateString() : 'N/A'}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(due.status || 'Pending')}`}>
                    {due.status || 'Pending'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => handleViewDetails(due)}
                    className="text-orange-500 hover:text-orange-500   ml-3 mt-1 p-2 rounded-full bg-orange-50 hover:bg-orange-100  text-orange-500 transition"
                  >
                    <Eye size={16} />
                    {/* <span>View</span> */}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between px-6 py-3 bg-white border-t border-gray-200">
          <div className="flex items-center">
            <p className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft size={20} />
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight size={20} />
            </button>
          </div>
        </div>
      )}
      </div>

      {/* Platform Due Detail Modal */}
      <PlatformDueDetail
        isOpen={isDetailOpen}
        onClose={handleCloseDetail}
        due={selectedDue}
      />
    </>
  );
};

export default PlatformDuesTable;
