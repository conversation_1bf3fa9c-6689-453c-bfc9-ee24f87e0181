import React, { useState, useEffect } from 'react';
import { X, CreditCard, AlertCircle } from 'lucide-react';
import { addDuesBankAccount, BankAccountData, hasDuesBankAccount, DuesBankAccount } from '../../services/dues';

interface AddDuesBankAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  duesPayerId: string;
  payerName: string;
  onSuccess: () => void;
  isReplacement?: boolean;
  existingAccount?: DuesBankAccount;
}

const AddDuesBankAccountModal: React.FC<AddDuesBankAccountModalProps> = ({
  isOpen,
  onClose,
  duesPayerId,
  payerName,
  onSuccess,
  isReplacement = false
}) => {
  const [formData, setFormData] = useState<BankAccountData>({
    bankName: '',
    accountNumber: '',
    routingNumber: '',
    accountType: 'checking',
    accountHolderName: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{
    bankName?: string;
    accountNumber?: string;
    routingNumber?: string;
    accountHolderName?: string;
  }>({});

  // Validation functions
  const validateBankName = (name: string): string | undefined => {
    if (!name.trim()) {
      return "Bank name is required";
    }
    if (name.trim().length < 2) {
      return "Bank name is too short";
    }
    return undefined;
  };

  const validateAccountNumber = (accountNumber: string): string | undefined => {
    if (!accountNumber.trim()) {
      return "Account number is required";
    }
    
    // Remove any spaces or dashes
    const cleanedNumber = accountNumber.replace(/[\s-]/g, '');
    
    // Most US bank accounts are between 8-17 digits
    if (!/^\d{8,17}$/.test(cleanedNumber)) {
      return "Account number should be 8-17 digits";
    }
    
    return undefined;
  };

  const validateRoutingNumber = (routingNumber: string): string | undefined => {
    if (!routingNumber.trim()) {
      return "Routing number is required";
    }
    
    // Remove any spaces or dashes
    const cleanedNumber = routingNumber.replace(/[\s-]/g, '');
    
    // US routing numbers are exactly 9 digits
    if (!/^\d{9}$/.test(cleanedNumber)) {
      return "Routing number should be exactly 9 digits";
    }
    
    // Additional check: Apply the ABA routing number checksum algorithm
    // This is a simple implementation of the algorithm
    let sum = 0;
    for (let i = 0; i < 9; i += 3) {
      sum += parseInt(cleanedNumber[i]) * 3;
      sum += parseInt(cleanedNumber[i + 1]) * 7;
      sum += parseInt(cleanedNumber[i + 2]) * 1;
    }
    
    if (sum % 10 !== 0) {
      return "Invalid routing number checksum";
    }
    
    return undefined;
  };

  const validateAccountHolderName = (name: string): string | undefined => {
    if (!name.trim()) {
      return "Account holder name is required";
    }
    if (name.trim().length < 2) {
      return "Account holder name is too short";
    }
    return undefined;
  };

  // Handle input changes with validation
  const handleInputChange = (field: keyof BankAccountData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear the error when the user starts typing again
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors = {
      bankName: validateBankName(formData.bankName),
      accountNumber: validateAccountNumber(formData.accountNumber),
      routingNumber: validateRoutingNumber(formData.routingNumber),
      accountHolderName: validateAccountHolderName(formData.accountHolderName)
    };
    
    setErrors(newErrors);
    
    // Form is valid if there are no error messages
    return !Object.values(newErrors).some(error => error !== undefined);
  };

  // State for success/error messages
  const [submitStatus, setSubmitStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string | null;
  }>({ type: null, message: null });

  // Check for existing account when component mounts
  useEffect(() => {
    const checkExistingAccount = async () => {
      if (duesPayerId && isOpen) {
        try {
          const { hasAccount, account } = await hasDuesBankAccount(duesPayerId);
          if (hasAccount && account && !isReplacement) {
            setSubmitStatus({
              type: 'error',
              message: 'Dues payer already has a bank account. Please use the update account function instead.'
            });
          }
        } catch (error) {
          console.error('Error checking for existing account:', error);
        }
      }
    };
    
    checkExistingAccount();
  }, [duesPayerId, isOpen, isReplacement]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset status message
    setSubmitStatus({ type: null, message: null });
    
    // Validate form before submission
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);

    try {
      // Clean the data before submission (remove spaces, dashes)
      const cleanedFormData = {
        ...formData,
        accountNumber: formData.accountNumber.replace(/[\s-]/g, ''),
        routingNumber: formData.routingNumber.replace(/[\s-]/g, '')
      };
      
      // Pass isReplacement flag to the API
      const result = await addDuesBankAccount(duesPayerId, cleanedFormData, isReplacement);
      
      if (result.success) {
        // Show success message
        setSubmitStatus({
          type: 'success',
          message: result.message || (isReplacement ? 'Bank account updated successfully' : 'Bank account added successfully')
        });
        
        // Reset form
        setFormData({
          bankName: '',
          accountNumber: '',
          routingNumber: '',
          accountType: 'checking',
          accountHolderName: ''
        });
        setErrors({});
        
        // Notify parent component
        onSuccess();
        
        // Close modal after a short delay to show success message
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        // Show error message
        setSubmitStatus({
          type: 'error',
          message: result.message || (isReplacement ? 'Failed to update bank account' : 'Failed to add bank account')
        });
      }
    } catch (error) {
      // Handle unexpected errors
      console.error('Error adding/replacing bank account:', error);
      setSubmitStatus({
        type: 'error',
        message: error instanceof Error ? error.message : (isReplacement ? 'Failed to replace bank account' : 'Failed to add bank account')
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            {isReplacement ? `Update Bank Account for ${payerName}` : `Add Bank Account for ${payerName}`}
          </h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X size={20} />
          </button>
        </div>
        
        {/* Status message */}
        {submitStatus.type && (
          <div className={`mb-4 p-3 rounded-lg ${
            submitStatus.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            <div className="flex items-center">
              {submitStatus.type === 'success' ? (
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <AlertCircle size={20} className="mr-2" />
              )}
              <span>{submitStatus.message}</span>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Bank Name
            </label>
            <input
              type="text"
              required
              value={formData.bankName}
              onChange={(e) => handleInputChange('bankName', e.target.value)}
              onBlur={() => setErrors({...errors, bankName: validateBankName(formData.bankName)})}
              className={`w-full px-3 py-2 border ${errors.bankName ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              placeholder="e.g., Chase Bank"
            />
            {errors.bankName && (
              <div className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle size={14} className="mr-1" />
                {errors.bankName}
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Account Holder Name
            </label>
            <input
              type="text"
              required
              value={formData.accountHolderName}
              onChange={(e) => handleInputChange('accountHolderName', e.target.value)}
              onBlur={() => setErrors({...errors, accountHolderName: validateAccountHolderName(formData.accountHolderName)})}
              className={`w-full px-3 py-2 border ${errors.accountHolderName ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              placeholder="Full name on account"
            />
            {errors.accountHolderName && (
              <div className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle size={14} className="mr-1" />
                {errors.accountHolderName}
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Account Number
            </label>
            <input
              type="text"
              required
              value={formData.accountNumber}
              onChange={(e) => handleInputChange('accountNumber', e.target.value)}
              onBlur={() => setErrors({...errors, accountNumber: validateAccountNumber(formData.accountNumber)})}
              className={`w-full px-3 py-2 border ${errors.accountNumber ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              placeholder="Account number (8-17 digits)"
            />
            {errors.accountNumber && (
              <div className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle size={14} className="mr-1" />
                {errors.accountNumber}
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Routing Number
            </label>
            <input
              type="text"
              required
              value={formData.routingNumber}
              onChange={(e) => handleInputChange('routingNumber', e.target.value)}
              onBlur={() => setErrors({...errors, routingNumber: validateRoutingNumber(formData.routingNumber)})}
              className={`w-full px-3 py-2 border ${errors.routingNumber ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              placeholder="9-digit routing number"
              maxLength={9}
            />
            {errors.routingNumber && (
              <div className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle size={14} className="mr-1" />
                {errors.routingNumber}
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Account Type
            </label>
            <select
              value={formData.accountType}
              onChange={(e) => setFormData(prev => ({ ...prev, accountType: e.target.value as 'checking' | 'savings' }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="checking">Checking</option>
              <option value="savings">Savings</option>
            </select>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center justify-center space-x-2"
            >
              <CreditCard size={16} />
              <span>
                {isSubmitting
                  ? (isReplacement ? 'Updating...' : 'Adding...')
                  : (isReplacement ? 'Update Account' : 'Add Account')}
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddDuesBankAccountModal;