import React, { useState, useEffect } from 'react';
import { Building2, Eye, EyeOff, Calendar, User, CreditCard, RefreshCw, CheckCircle, AlertCircle, Plus, Trash2 } from 'lucide-react';
import { getDuesBankAccounts, getDuesBankAccountsById, DuesBankAccount, hasDuesBankAccount, addDuesBankAccount } from '../../services/dues';
import PaymentLoader from '../common/PaymentLoader';
import AddDuesBankAccountModal from './AddDuesBankAccountModal';

// Mock function to simulate unmasking account numbers
// In a real implementation, this would be a secure API call to get the full account number
const unmaskAccountNumber = (maskedNumber: string): string => {
  // This is just a simulation - in a real app, you would make an API call
  // to get the actual unmasked account number with proper authentication
  if (maskedNumber.startsWith('****')) {
    const lastFour = maskedNumber.slice(-4);
    return `**********${lastFour}`;
  }
  return maskedNumber;
};

interface DuesBankAccountsProps {
  duesPayerId?: string;
  payerName?: string;
  showAddButton?: boolean;
  showAddBankModal?: boolean;
  setShowAddBankModal?: (show: boolean) => void;
  onAccountsCountChange?: (count: number) => void; // Add this callback
}

const DuesBankAccounts: React.FC<DuesBankAccountsProps> = ({ 
  duesPayerId, 
  payerName,
  showAddButton = true,
  showAddBankModal: externalShowAddBankModal,
  setShowAddBankModal: externalSetShowAddBankModal,
  onAccountsCountChange // Add this prop
}) => {
  // Use internal state if external state is not provided
  const [internalShowAddBankModal, setInternalShowAddBankModal] = useState(false);
  
  // Use either the external or internal state
  const showAddBankModal = externalShowAddBankModal !== undefined ? 
    externalShowAddBankModal : internalShowAddBankModal;
  
  const setShowAddBankModal = externalSetShowAddBankModal || setInternalShowAddBankModal;
  const [accounts, setAccounts] = useState<DuesBankAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [visibleAccounts, setVisibleAccounts] = useState<Set<number>>(new Set());
  const [refreshing, setRefreshing] = useState(false);
  const [unmaskedAccounts, setUnmaskedAccounts] = useState<Record<number, string>>({});
  const [statusMessage, setStatusMessage] = useState<{
    type: 'success' | 'error' | null;
    message: string | null;
  }>({ type: null, message: null });
  const [updatingPrimary, setUpdatingPrimary] = useState<number | null>(null);
  const [updatingStatus, setUpdatingStatus] = useState<number | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [accountToToggle, setAccountToToggle] = useState<{account: DuesBankAccount, newStatus: 'active' | 'inactive'} | null>(null);

  useEffect(() => {
    fetchBankAccounts();
  }, [duesPayerId]);

  const fetchBankAccounts = async () => {
    try {
      setLoading(true);
      let data;
      
      if (duesPayerId) {
        const response = await getDuesBankAccountsById(duesPayerId);
        data = response;
      } else {
        data = await getDuesBankAccounts();
      }
      
      setAccounts(data);
      setError(null);
      
      // Report the count back to parent component
      if (onAccountsCountChange) {
        onAccountsCountChange(data.length);
      }
    } catch (err) {
      setError('Failed to fetch dues bank accounts');
      if (onAccountsCountChange) {
        onAccountsCountChange(0);
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const refreshAccounts = () => {
    setRefreshing(true);
    fetchBankAccounts();
  };

  const toggleAccountVisibility = (accountId: number, maskedNumber: string) => {
    const newVisible = new Set(visibleAccounts);
    
    if (newVisible.has(accountId)) {
      // Hide the account number
      newVisible.delete(accountId);
    } else {
      // Show the unmasked account number
      newVisible.add(accountId);
      
      // If we don't have the unmasked version yet, generate it
      if (!unmaskedAccounts[accountId]) {
        const unmaskedNumber = unmaskAccountNumber(maskedNumber);
        setUnmaskedAccounts(prev => ({
          ...prev,
          [accountId]: unmaskedNumber
        }));
      }
    }
    
    setVisibleAccounts(newVisible);
  };

  // Handle replacing a bank account
  const handleReplaceAccount = async (account: DuesBankAccount) => {
    try {
      setStatusMessage({ type: null, message: null });
      
      // Show confirmation dialog for replacing account
      setAccountToToggle({
        account,
        newStatus: 'inactive'
      });
      setShowConfirmDialog(true);
    } catch (err) {
      console.error('Error preparing to replace account:', err);
      setStatusMessage({
        type: 'error',
        message: 'An unexpected error occurred while preparing to replace the bank account'
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex items-center justify-center py-12">
          <PaymentLoader
            type="setup"
            message="Loading dues bank accounts..."
            size="medium"
            showQuotes={true}
          />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  // Confirmation dialog for replacing bank accounts
  const ConfirmationDialog = () => {
    if (!accountToToggle) return null;
    
    const { account } = accountToToggle;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
          <div className="flex items-center mb-4">
            <RefreshCw className="text-blue-500 mr-3" size={24} />
            <h3 className="text-lg font-semibold text-gray-900">
              Update Bank Account
            </h3>
          </div>

          <p className="text-gray-600 mb-4">
            Update the bank account details for <span className="font-medium">{account.payerName}</span>.
            The current <span className="font-medium">{account.bankName}</span> account information will be replaced.
          </p>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6">
            <div className="flex items-center text-blue-800">
              <RefreshCw size={16} className="mr-2" />
              <p className="text-sm">
                You'll be able to enter new bank account details. The existing account will be updated with the new information.
              </p>
            </div>
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowConfirmDialog(false)}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                setShowConfirmDialog(false);
                // Show the add bank account modal with isReplacement=true
                setShowAddBankModal(true);
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Update Account
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      {showConfirmDialog && <ConfirmationDialog />}
      {showAddBankModal && duesPayerId && payerName && (
        <AddDuesBankAccountModal
          isOpen={showAddBankModal}
          onClose={() => setShowAddBankModal(false)}
          duesPayerId={duesPayerId}
          payerName={payerName}
          isReplacement={accounts.length > 0}
          existingAccount={accounts.length > 0 ? accounts[0] : undefined}
          onSuccess={() => {
            refreshAccounts();
            setShowAddBankModal(false);
          }}
        />
      )}
      
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Building2 className="text-blue-600" size={24} />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Dues Bank Account</h2>
            <p className="text-sm text-gray-500">Manage dues payer's bank account</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          {showAddButton && duesPayerId && (
            <button
              onClick={async () => {
                // Check if dues payer already has an account
                if (duesPayerId) {
                  const { hasAccount } = await hasDuesBankAccount(duesPayerId);
                  if (hasAccount && accounts.length > 0) {
                    // For single account, show confirmation dialog for updating/replacing
                    setAccountToToggle({
                      account: accounts[0],
                      newStatus: 'inactive'
                    });
                    setShowConfirmDialog(true);
                  } else {
                    // Show add account modal
                    setShowAddBankModal(true);
                  }
                } else {
                  setShowAddBankModal(true);
                }
              }}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-1"
              title={accounts.length > 0 ? "Update bank account details" : "Add new bank account"}
            >
              {accounts.length > 0 ? (
                <>
                  <RefreshCw size={14} />
                  <span>Update Account</span>
                </>
              ) : (
                <>
                  <Plus size={14} />
                  <span>Add Account</span>
                </>
              )}
            </button>
          )}
          <button 
            onClick={refreshAccounts}
            className="p-2 rounded-full bg-blue-50 hover:bg-blue-100 text-blue-600 transition"
            disabled={refreshing}
            title="Refresh account"
          >
            <RefreshCw size={16} className={refreshing ? "animate-spin" : ""} />
          </button>
          <div className="bg-blue-50 px-3 py-1 rounded-full">
            <span className="text-sm font-medium text-blue-700">{accounts.filter(acc => acc.status === 'active').length} Active Account</span>
          </div>
        </div>
      </div>

      {/* Status message */}
      {statusMessage.type && (
        <div className={`mb-4 p-3 rounded-lg ${
          statusMessage.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          <div className="flex items-center">
            {statusMessage.type === 'success' ? (
              <CheckCircle size={20} className="mr-2" />
            ) : (
              <AlertCircle size={20} className="mr-2" />
            )}
            <span>{statusMessage.message}</span>
          </div>
        </div>
      )}

      {accounts.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <Building2 className="text-gray-400 w-8 h-8" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Bank Accounts</h3>
          <p className="text-gray-500">No dues bank accounts have been added yet.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {accounts.map((account) => (
            <div
              key={account.id}
              className={`border rounded-lg p-4 hover:shadow-md transition-shadow ${
                account.isPrimary ? 'border-blue-300 bg-blue-50' : 'border-gray-200'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center bg-blue-100">
                    <User className="text-blue-600 w-6 h-6" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-semibold text-gray-900">{account.payerName}</h4>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                        Active
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <Building2 size={14} />
                        <span>{account.bankName}</span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <CreditCard size={14} />
                        <span className="capitalize">{account.accountType}</span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Calendar size={14} />
                        <span>Added {formatDate(account.createdAt)}</span>
                      </div>
                    </div>
                    
                    <div className="mt-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">Account:</span>
                        <span className="text-sm font-mono">
                          {visibleAccounts.has(account.id)
                            ? unmaskedAccounts[account.id] || account.accountNumber
                            : account.accountNumber
                          }
                        </span>
                        <button
                          onClick={() => toggleAccountVisibility(account.id, account.accountNumber)}
                          className="text-gray-400 hover:text-gray-600"
                          title={visibleAccounts.has(account.id) ? "Hide account number" : "Show full account number"}
                        >
                          {visibleAccounts.has(account.id) ? (
                            <EyeOff size={14} />
                          ) : (
                            <Eye size={14} />
                          )}
                        </button>
                      </div>
                      
                      <div className="text-sm text-gray-500 mt-1">
                        Account Holder: {account.accountHolderName}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="ml-4">
                  <button
                    onClick={() => {
                      setAccountToToggle({
                        account,
                        newStatus: 'inactive'
                      });
                      setShowConfirmDialog(true);
                    }}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-1"
                  >
                    <Trash2 size={14} />
                    <span>Replace</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DuesBankAccounts;