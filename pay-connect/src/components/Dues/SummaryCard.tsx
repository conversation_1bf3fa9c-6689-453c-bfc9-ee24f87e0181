import React from 'react';
import { DollarSign, CheckCircle, Clock, XCircle } from 'lucide-react';

interface SummaryCardProps {
  title: string;
  value: number;
  className?: string;
  onClick?: () => void;
  selected?: boolean;
}

const SummaryCard: React.FC<SummaryCardProps> = ({ title, value, className, onClick, selected }) => {
  const getIcon = () => {
    switch (title) {
      case 'Total Dues':
        return <DollarSign size={24} className="text-blue-600" />;
      case 'Paid Dues':
        return <CheckCircle size={24} className="text-green-600" />;
      case 'Pending Dues':
        return <Clock size={24} className="text-yellow-600" />;
      case 'Overdue Dues':
        return <XCircle size={24} className="text-red-600" />;
      default:
        return <DollarSign size={24} className="text-gray-600" />;
    }
  };

  return (
    <div
      className={`
    bg-white rounded-xl shadow-sm border p-5 cursor-pointer h-[120px] flex items-center justify-between space-x-4
    ${className}
    ${selected ? 'bg-blue-100 border-2 border-blue-500' : ''}
  `}
      onClick={onClick}
    >
      <div>
        <p className={`text-2xl font-bold ${selected ? 'text-blue-600' : 'text-gray-900'}`}>
          {value}
        </p>
        <p className={`${selected ? 'text-blue-500' : 'text-gray-500'}`}>
          {title}
        </p>
      </div>

      <div className={`w-12 h-12 rounded-full border flex items-center justify-center bg-gray-100${selected ? 'border border-blue-600  bg-white ' : ''}`}>
        {getIcon()}
      </div>
    </div>



  );
};

export default SummaryCard; 