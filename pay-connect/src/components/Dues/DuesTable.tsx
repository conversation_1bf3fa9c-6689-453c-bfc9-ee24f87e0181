import React, { useState, useEffect } from 'react';
import DuesTableRow from './DuesTableRow';
import { getDues, deleteDue, Due } from '../../services/dues';
import { ChevronLeft, ChevronRight, CreditCard } from 'lucide-react';
import EmptyState from '../common/EmptyState';
import DeleteConfirmModal from './DeleteConfirmModal';
import PaymentLoader from '../common/PaymentLoader';
import AddDuesBankAccountModal from './AddDuesBankAccountModal';
import StaffBankTransferModal from '../Staff/StaffBankTransferModal';

interface DuesTableProps {
  className?: string;
  search: string;
  statusFilter: string;
  onDataChange: () => void; // Callback to re-fetch summary when data changes
  onPayNow: (due: Due) => void; // Callback to handle pay now action
  onBulkPay: (dues: Due[]) => void; // Callback to handle bulk pay action
}

const DuesTable: React.FC<DuesTableProps> = ({
  className,
  search,
  statusFilter,
  onDataChange,
  onPayNow,
  onBulkPay,
}) => {
  const [dues, setDues] = useState<Due[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedDues, setSelectedDues] = useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  
  // Modal state management - copying from StaffTable
  const [showAddBankModal, setShowAddBankModal] = useState(false);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [selectedDueForBank, setSelectedDueForBank] = useState<Due | null>(null);
  const [selectedDueForTransfer, setSelectedDueForTransfer] = useState<Due | null>(null);

  // Handler functions - copying from StaffTable
  const handleAddBankAccount = (due: Due) => {
    setSelectedDueForBank(due);
    setShowAddBankModal(true);
  };

  const handleBankTransfer = (due: Due) => {
    setSelectedDueForTransfer(due);
    setShowTransferModal(true);
  };

  const handleBankSuccess = () => {
    // Refresh dues data after bank operations
    refreshData();
    setSelectedDueForBank(null);
    setSelectedDueForTransfer(null);
  };

  useEffect(() => {
    fetchDues();
  }, [search, statusFilter, currentPage]);

  useEffect(() => {
    // Update select all state based on current page selections
    const currentDues = dues.slice((currentPage - 1) * 10, currentPage * 10);
    const selectableDues = currentDues.filter(due => due.status === 'Pending' || due.status === 'Overdue');
    const allSelected = selectableDues.length > 0 && selectableDues.every(due => selectedDues.has(due.id));
    setSelectAll(allSelected);
  }, [selectedDues, dues, currentPage]);

  const fetchDues = async () => {
    try {
      setLoading(true);
      
      // Only add a small delay for better UX - copying from StaffTable
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const { data, totalPages: newTotalPages } = await getDues({
        search,
        status: statusFilter !== 'All' ? (statusFilter as Due['status']) : undefined,
      });
      setDues(data);
      setTotalPages(newTotalPages);
      setError(null);
    } catch (err) {
      console.error('Error fetching dues:', err);
      setError('Failed to fetch dues. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Function to refresh data after operations - copying from StaffTable
  const refreshData = () => {
    fetchDues();
  };

const [showDeleteModal, setShowDeleteModal] = useState(false);
const [selectedDueId, setSelectedDueId] = useState<string | null>(null);


const handleDeleteClick = (id: string) => {
  setSelectedDueId(id);
  setShowDeleteModal(true);
};

const confirmDelete = async () => {
  if (!selectedDueId) return;

  try {
    await deleteDue(selectedDueId);

    setDues(prev => prev.filter(due => due.id !== selectedDueId));
    setSelectedDues(prev => {
      const newSet = new Set(prev);
      newSet.delete(selectedDueId);
      return newSet;
    });
    onDataChange();
    alert(`Due item with ID ${selectedDueId} deleted (static data only).`);
  } catch (err) {
    setError('Failed to delete due item');
  } finally {
    setSelectedDueId(null);
    setShowDeleteModal(false);
  }
};
  const handleSelectAll = (checked: boolean) => {
    const currentDues = dues.slice((currentPage - 1) * 10, currentPage * 10);
    const selectableDues = currentDues.filter(due => due.status === 'Pending' || due.status === 'Overdue');
    
    setSelectedDues(prev => {
      const newSet = new Set(prev);
      if (checked) {
        selectableDues.forEach(due => newSet.add(due.id));
      } else {
        selectableDues.forEach(due => newSet.delete(due.id));
      }
      return newSet;
    });
  };

  const handleSelectDue = (id: string, checked: boolean) => {
    setSelectedDues(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(id);
      } else {
        newSet.delete(id);
      }
      return newSet;
    });
  };

  const handleBulkPay = () => {
    const selectedDueObjects = dues.filter(due => selectedDues.has(due.id));
    if (selectedDueObjects.length > 0) {
      onBulkPay(selectedDueObjects);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <PaymentLoader
          type="setup"
          message="Loading dues..."
          size="small"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        {error}
      </div>
    );
  }

  const startIndex = (currentPage - 1) * 10;
  const endIndex = Math.min(startIndex + 10, dues.length);
  const currentDues = dues.slice(startIndex, endIndex);
  const selectedCount = selectedDues.size;

  // Check if we should show empty state
  const hasSearchOrFilter = search.trim() !== '' || statusFilter !== 'All';
  const showEmptyState = dues.length === 0;

  if (showEmptyState) {
    return (
      <div className={`rounded-md border border-gray-200 ${className}`}>
        <EmptyState
          type={hasSearchOrFilter ? 'search' : 'dues'}
          title={hasSearchOrFilter ? 'No dues found' : undefined}
          description={hasSearchOrFilter ? 'Try adjusting your search criteria or filters.' : undefined}
          onAction={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      <div className={`overflow-x-auto rounded-md ${className}`}>
      {/* Bulk Actions Bar */}
      {selectedCount > 0 && (
        <div className="bg-blue-50 border-b border-blue-200 px-6 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-blue-900">
              {selectedCount} due{selectedCount !== 1 ? 's' : ''} selected
            </span>
            <button
              onClick={() => setSelectedDues(new Set())}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Clear Selection
            </button>
          </div>
          <button
            onClick={handleBulkPay}
            className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            <CreditCard size={16} />
            <span>Pay Selected ({selectedCount})</span>
          </button>
        </div>
      )}

      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-[#404040]">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              <input 
                type="checkbox" 
                className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
                checked={selectAll}
                onChange={(e) => handleSelectAll(e.target.checked)}
              />
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Payer Name
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Email
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Contact
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              League
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Season
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Amount
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Due Date
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Status
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Action
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {currentDues.map((due) => (
            <DuesTableRow 
              key={due.id} 
              due={due} 
              onDelete={() => handleDeleteClick(due.id)}
              onPayNow={onPayNow}
              onAddBankAccount={handleAddBankAccount}
              onBankTransfer={handleBankTransfer}
              isSelected={selectedDues.has(due.id)}
              onSelect={(checked) => handleSelectDue(due.id, checked)}
            />
          ))}
        </tbody>
      </table>
      <nav
        className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
        aria-label="Pagination"
      >
        <div className="flex-1 flex justify-between sm:justify-end items-center">
          <p className="text-sm text-gray-700 mr-4">
            Showing <span className="font-medium">{startIndex + 1}</span> to <span className="font-medium">{endIndex}</span> of {' '}
            <span className="font-medium">{dues.length}</span> results
          </p>
          <div className="flex">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="sr-only">Previous</span>
              <ChevronLeft className="h-5 w-5" aria-hidden="true" />
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="sr-only">Next</span>
              <ChevronRight className="h-5 w-5" aria-hidden="true" />
            </button>
          </div>
        </div>
      </nav>



      <DeleteConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={confirmDelete}
        itemId={selectedDueId}
        itemType="due item"
      />
      </div>
      
      {/* Modal components - copying from StaffTable */}
      <AddDuesBankAccountModal
        isOpen={showAddBankModal}
        onClose={() => setShowAddBankModal(false)}
        duesPayerId={selectedDueForBank?.id || ''}
        payerName={selectedDueForBank?.firstname || ''}
        onSuccess={handleBankSuccess}
      />
      <StaffBankTransferModal
        isOpen={showTransferModal}
        onClose={() => setShowTransferModal(false)}
        staffId={selectedDueForTransfer?.id || ''}
        staffName={selectedDueForTransfer?.firstname || ''}
        onSuccess={handleBankSuccess}
      />
    </>
  );
};

export default DuesTable; 