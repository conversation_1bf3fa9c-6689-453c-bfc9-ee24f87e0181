import React from 'react';
import { Due } from '../../services/dues';
import { Eye, Trash2, CreditCard } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface DuesTableRowProps {
  due: Due;
  onDelete: (id: string) => void;
  onPayNow: (due: Due) => void;
  isSelected?: boolean;
  onSelect?: (checked: boolean) => void;
}

const DuesTableRow: React.FC<DuesTableRowProps> = ({ 
  due, 
  onDelete, 
  onPayNow, 
  isSelected = false, 
  onSelect 
}) => {
  const navigate = useNavigate();

  const getStatusColor = (status: Due['status']) => {
    switch (status) {
      case 'Paid':
        return 'text-green-600';
      case 'Pending':
        return 'text-yellow-600';
      case 'Overdue':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const isOverdue = due.status === 'Overdue';
  const canPay = due.status === 'Pending' || due.status === 'Overdue';
  const canSelect = due.status === 'Pending' || due.status === 'Overdue';

  const handleViewDetails = () => {
    navigate(`/dues/${due.id}`);
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onSelect) {
      onSelect(e.target.checked);
    }
  };

  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        {canSelect ? (
          <input 
            type="checkbox" 
            className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
            checked={isSelected}
            onChange={handleSelectChange}
          />
        ) : (
          <div className="w-4 h-4"></div>
        )}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
        <div className="flex items-center space-x-3">
         <img src={due.avatarUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(due.firstname)}`} className="h-8 w-8 rounded-full mr-3" />

         
          <span>{due.firstname}</span>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {due.email}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {due.contact}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {due.game_title}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {due.season_name}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        ${due.amount}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {new Date(due.end_date,).toLocaleDateString()}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <span className={getStatusColor(due.status)}>
          {due.status}
          {isOverdue && ' (10 days)'}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex items-center space-x-2">
          <button 
            onClick={handleViewDetails}
            className="text-orange-500 hover:text-orange-500   ml-3 mt-1 p-2 rounded-full bg-orange-50 hover:bg-orange-100  text-orange-500 transition"
            title="View Details"
          >
            <Eye size={18} />
          </button>
          {canPay && (
            <button 
              onClick={() => onPayNow(due)}
              className="text-green-600 hover:text-green-900"
              title="Pay Now"
            >
              <CreditCard size={18} />
            </button>
          )}
          <button 
            onClick={() => onDelete(due.id)}
            className="text-red-600 hover:text-red-900"
            title="Delete"
          >
            <Trash2 size={18} />
          </button>
        </div>
      </td>
    </tr>
  );
};

export default DuesTableRow; 