import React, { useState, useEffect } from 'react';
import { X, Building2, DollarSign, AlertCircle, Lock, Eye, EyeOff, ChevronDown, CreditCard } from 'lucide-react';
import { getConnectedAccounts, ConnectedBankAccount } from '../../services/plaid';
import { getPaymentMethods, PaymentMethod } from '../../services/paymentMethods';

interface WithdrawModalProps {
  isOpen: boolean;
  onClose: () => void;
  onWithdraw: (amount: number, pin: string, bankAccountId?: string, paymentMethod?: string) => Promise<{ success: boolean; message?: string }>;
  currentBalance: number;
  primaryBank: string;
}

const WithdrawModal: React.FC<WithdrawModalProps> = ({
  isOpen,
  onClose,
  onWithdraw,
  currentBalance,
  primaryBank
}) => {
  const [amount, setAmount] = useState('');
  const [pin, setPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPin, setShowPin] = useState(false);
  const [connectedAccounts, setConnectedAccounts] = useState<ConnectedBankAccount[]>([]);
  const [selectedBankAccount, setSelectedBankAccount] = useState<ConnectedBankAccount | null>(null);
  const [showBankDropdown, setShowBankDropdown] = useState(false);
  const [loadingAccounts, setLoadingAccounts] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [showPaymentDropdown, setShowPaymentDropdown] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loadingPaymentMethods, setLoadingPaymentMethods] = useState(false);

  const quickAmounts = [50, 100, 200, 500];

  // Fetch connected accounts and payment methods when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchConnectedAccounts();
      fetchPaymentMethods();
    }
  }, [isOpen]);

  const fetchConnectedAccounts = async () => {
    try {
      setLoadingAccounts(true);
      const accounts = await getConnectedAccounts();
      setConnectedAccounts(accounts);

      // Set primary account as default selection
      const primaryAccount = accounts.find(account => account.is_primary);
      if (primaryAccount) {
        setSelectedBankAccount(primaryAccount);
      } else if (accounts.length > 0) {
        setSelectedBankAccount(accounts[0]);
      }
    } catch (error) {
      console.error('Error fetching connected accounts:', error);
      setError('Failed to load bank accounts. Please try again.');
    } finally {
      setLoadingAccounts(false);
    }
  };

  const fetchPaymentMethods = async () => {
    try {
      setLoadingPaymentMethods(true);
      const methods = await getPaymentMethods('withdrawal');
      setPaymentMethods(methods);

      // Set first method as default selection
      if (methods.length > 0) {
        setSelectedPaymentMethod(methods[0]);
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      setError('Failed to load payment methods. Please try again.');
    } finally {
      setLoadingPaymentMethods(false);
    }
  };

  const getAccountIcon = (type: string, subtype: string | null) => {
    if (type === 'depository') {
      if (subtype === 'checking') return <Building2 size={16} className="text-blue-600" />;
      if (subtype === 'savings') return <Building2 size={16} className="text-green-600" />;
    }
    return <CreditCard size={16} className="text-gray-600" />;
  };

  const formatAccountType = (type: string, subtype: string | null) => {
    if (type === 'depository') {
      if (subtype === 'checking') return 'Checking';
      if (subtype === 'savings') return 'Savings';
    }
    return 'Account';
  };

  const getSelectedPaymentMethod = () => {
    return selectedPaymentMethod || (paymentMethods.length > 0 ? paymentMethods[0] : null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const numAmount = parseFloat(amount);
    if (!numAmount || numAmount <= 0) {
      setError('Please enter a valid amount');
      return;
    }

    if (numAmount < 10) {
      setError('Minimum withdrawal amount is $10');
      return;
    }

    if (numAmount > currentBalance) {
      setError('Insufficient balance');
      return;
    }

    if (numAmount > 5000) {
      setError('Maximum withdrawal amount is $5,000 per transaction');
      return;
    }

    if (!pin || pin.length < 4) {
      setError('Please enter your 4-6 digit wallet PIN');
      return;
    }

    if (!selectedBankAccount) {
      setError('Please select a bank account');
      return;
    }

    // Add fee to the amount if applicable
    const selectedMethod = getSelectedPaymentMethod();
    if (!selectedMethod) {
      setError('Please select a payment method');
      return;
    }

    const fee = Number(selectedMethod.fee || 0);
    const totalAmount = numAmount + fee;

    if (totalAmount > currentBalance) {
      setError(`Insufficient balance. Total amount including ${fee > 0 ? `$${fee.toFixed(2)} fee` : 'no fee'}: $${totalAmount.toFixed(2)}`);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await onWithdraw(numAmount, pin, selectedBankAccount.id, selectedMethod.method_id);

      if (result.success) {
        setAmount('');
        setPin('');
        onClose();
      } else {
        setError(result.message || 'Failed to withdraw money');
      }
    } catch (err) {
      setError('Failed to withdraw money. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickAmount = (quickAmount: number) => {
    if (quickAmount <= currentBalance) {
      setAmount(quickAmount.toString());
      setError(null);
    } else {
      setError('Insufficient balance for this amount');
    }
  };

  if (!isOpen) return null;

  return (
    <>

      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-xl shadow-xl max-w-2xl  overflow-y-auto">
          {/* Header */}
          <div className="flex items-start justify-between p-6 border-b">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Withdraw Money</h2>
              {/* Current Balance */}
              <div className=" flex items-center space-x-2  ">
                <p className="text-sm text-blue-600 mb-1">Available Balance</p>
                <p className="text-2xl font-bold text-blue-900">${currentBalance.toFixed(2)}</p>
              </div>

            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X size={20} />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">

            {/*  */}
            <div className=' max-h-[580px] overflow-y-auto  '>


              {/* Bank Account Selection */}
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Select Destination Bank Account</h3>
                {loadingAccounts ? (
                  <div className="bg-gray-50 rounded-lg p-4 text-center">
                    <p className="text-sm text-gray-500">Loading bank accounts...</p>
                  </div>
                ) : connectedAccounts.length === 0 ? (
                  <div className="bg-red-50 rounded-lg p-4 text-center">
                    <p className="text-sm text-red-600">No bank accounts connected. Please connect a bank account first.</p>
                  </div>
                ) : (
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setShowBankDropdown(!showBankDropdown)}
                      className="w-full bg-white border border-gray-300 rounded-lg p-3 flex items-center justify-between hover:border-blue-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-colors"
                    >
                      {selectedBankAccount ? (
                        <div className="flex items-center space-x-3">
                          {getAccountIcon(selectedBankAccount.type, selectedBankAccount.subtype)}
                          <div className="text-left">
                           <div className="flex items-center space-x-2">
                             <p className="font-medium text-gray-900">{selectedBankAccount.name}</p>
                             {selectedBankAccount.is_primary && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 mt-1">
                                Primary
                              </span>
                            )}
                           </div>
                            <p className="text-sm text-gray-500">
                              {selectedBankAccount.institution_name} • {formatAccountType(selectedBankAccount.type, selectedBankAccount.subtype)} • ••••{selectedBankAccount.mask}
                            </p>
                           
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-500">Select a bank account</span>
                      )}
                      <ChevronDown size={20} className={`text-gray-400 transition-transform ${showBankDropdown ? 'rotate-180' : ''}`} />
                    </button>

                    {showBankDropdown && (
                      <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                        {connectedAccounts.map((account) => (
                          <button
                            key={account.id}
                            type="button"
                            onClick={() => {
                              setSelectedBankAccount(account);
                              setShowBankDropdown(false);
                              setError(null);
                            }}
                            className="w-full p-4 text-left hover:bg-gray-50 flex items-center space-x-3 border-b border-gray-100 last:border-b-0"
                          >
                            {getAccountIcon(account.type, account.subtype)}
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <p className="font-medium text-gray-900">{account.name}</p>
                                {account.is_primary && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                                    Primary
                                  </span>
                                )}
                              </div>
                              <p className="text-sm text-gray-500">
                                {account.institution_name} • {formatAccountType(account.type, account.subtype)} • ••••{account.mask}
                              </p>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Payment Method Selection */}
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Select Payment Method</h3>
                {loadingPaymentMethods ? (
                  <div className="bg-gray-50 rounded-lg p-4 text-center">
                    <p className="text-sm text-gray-500">Loading payment methods...</p>
                  </div>
                ) : paymentMethods.length === 0 ? (
                  <div className="bg-red-50 rounded-lg p-4 text-center">
                    <p className="text-sm text-red-600">No payment methods available. Please try again later.</p>
                  </div>
                ) : (
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setShowPaymentDropdown(!showPaymentDropdown)}
                      className="w-full bg-white border border-gray-300 rounded-lg p-4 flex items-center justify-between hover:border-blue-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-colors"
                    >
                      {selectedPaymentMethod ? (
                        <div className="text-left">
                          <p className="font-medium text-gray-900">{selectedPaymentMethod.name}</p>
                          <p className="text-sm text-gray-500">{selectedPaymentMethod.description}</p>
                          <div className="flex items-center space-x-4 mt-1">
                            <span className="text-xs text-blue-600">{selectedPaymentMethod.processing_time}</span>
                            <span className="text-xs text-gray-600">
                              Fee: {Number(selectedPaymentMethod.fee || 0) === 0 ? 'Free' : `$${Number(selectedPaymentMethod.fee || 0).toFixed(2)}`}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-500">Select a payment method</span>
                      )}
                      <ChevronDown size={20} className={`text-gray-400 transition-transform ${showPaymentDropdown ? 'rotate-180' : ''}`} />
                    </button>

                    {showPaymentDropdown && (
                      <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                        {paymentMethods.map((method) => (
                          <button
                            key={method.id}
                            type="button"
                            onClick={() => {
                              setSelectedPaymentMethod(method);
                              setShowPaymentDropdown(false);
                              setError(null);
                            }}
                            className="w-full p-4 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                          >
                            <div>
                              <p className="font-medium text-gray-900">{method.name}</p>
                              <p className="text-sm text-gray-500">{method.description}</p>
                              <div className="flex items-center space-x-4 mt-1">
                                <span className="text-xs text-blue-600">{method.processing_time}</span>
                                <span className="text-xs text-gray-600">
                                  Fee: {Number(method.fee || 0) === 0 ? 'Free' : `$${Number(method.fee || 0).toFixed(2)}`}
                                </span>
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Quick Amount Selection */}
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Select</h3>
                <div className="grid grid-cols-4 gap-2">
                  {quickAmounts.map((quickAmount) => (
                    <button
                      key={quickAmount}
                      onClick={() => handleQuickAmount(quickAmount)}
                      disabled={quickAmount > currentBalance}
                      className={`p-2 text-sm font-medium rounded-lg transition-colors ${quickAmount <= currentBalance
                        ? 'text-blue-600 bg-blue-50 hover:bg-blue-100'
                        : 'text-gray-400 bg-gray-100 cursor-not-allowed'
                        }`}
                    >
                      ${quickAmount}
                    </button>
                  ))}
                </div>
              </div>

              {/* Amount Input */}
              <form onSubmit={handleSubmit}>
                <div className="mb-6">
                  <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                    Enter Amount
                  </label>
                  <div className="relative">
                    <DollarSign size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="number"
                      id="amount"
                      value={amount}
                      onChange={(e) => {
                        setAmount(e.target.value);
                        setError(null);
                      }}
                      placeholder="0.00"
                      min="10"
                      max={Math.min(currentBalance, 5000)}
                      step="0.01"
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                  <p className="text-s text-gray-500 mt-1 ">
                    Minimum: $10 | Maximum: ${Math.min(currentBalance, 5000).toFixed(2)}
                  </p>
                </div>

                {/* PIN Input */}
                <div className="mb-6">
                  <label htmlFor="pin" className="block text-sm font-medium text-gray-700 mb-2">
                    Wallet PIN
                  </label>
                  <div className="relative">
                    <Lock size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type={showPin ? 'text' : 'password'}
                      id="pin"
                      value={pin}
                      onChange={(e) => {
                        setPin(e.target.value);
                        setError(null);
                      }}
                      placeholder="Enter your wallet PIN"
                      maxLength={6}
                      className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPin(!showPin)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPin ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                  <div className="flex items-center justify-between mt-1">
                    <p className="text-xs text-gray-500">Enter your 4-6 digit wallet PIN to authorize the withdrawal</p>
                    <button
                      type="button"
                      onClick={() => window.open('/wallet?tab=settings', '_blank')}
                      className="text-xs text-blue-600 hover:text-blue-800 underline"
                    >
                      Forgot PIN?
                    </button>
                  </div>
                </div>

                {/* Error Message */}
                {error && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-600">{error}</p>
                  </div>
                )}

                {/* Transaction Summary */}
                {amount && parseFloat(amount) > 0 && selectedPaymentMethod && (
                  <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Transaction Summary</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Withdrawal Amount:</span>
                        <span className="text-gray-900">${parseFloat(amount).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Processing Fee:</span>
                        <span className="text-gray-900">
                          {Number(selectedPaymentMethod.fee || 0) === 0 ? 'Free' : `$${Number(selectedPaymentMethod.fee || 0).toFixed(2)}`}
                        </span>
                      </div>
                      <div className="border-t border-gray-200 pt-2 mt-2">
                        <div className="flex justify-between text-sm font-medium">
                          <span className="text-gray-900">Total Deducted:</span>
                          <span className="text-gray-900">
                            ${(parseFloat(amount) + Number(selectedPaymentMethod.fee || 0)).toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Processing Time Notice */}
                {selectedPaymentMethod && (
                  <div className="mb-6 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start space-x-2">
                      <AlertCircle size={16} className="text-blue-600 mt-0.5" />
                      <div>
                        <p className="text-sm text-blue-800 font-medium">Processing Time</p>
                        <p className="text-xs text-blue-700">
                          {selectedPaymentMethod.name} withdrawals take {selectedPaymentMethod.processing_time.toLowerCase()} to reflect in your bank account.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="flex-1 px-4 py-3 bg-red-600 text-white rounded-lg font-medium transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={
                      isLoading ||
                      !amount ||
                      !pin ||
                      !selectedBankAccount ||
                      !selectedPaymentMethod ||
                      parseFloat(amount) + Number(selectedPaymentMethod?.fee || 0) > currentBalance
                    }
                    className="flex-1 px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? 'Processing...' : 'Withdraw'}
                  </button>
                </div>
              </form>

              {/* Security Notice */}
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <p className="text-xs text-gray-600">
                  🔒 All withdrawals are secured and processed through encrypted channels.
                  You will receive a confirmation email once the transfer is initiated.
                </p>
              </div>

            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default WithdrawModal;
