import React from 'react';
import TransactionHistoryItem from './TransactionHistoryItem';

interface TransactionHistoryGroupProps {
  dateHeader: string;
  items: Array<{
    title: string;
    subtitle: string;
    amount: string;
    date: string;
    type: 'received' | 'withdrawn' | 'refund' | 'pending';
    status?: string;
    from: string;
  }>;
}

const TransactionHistoryGroup: React.FC<TransactionHistoryGroupProps> = ({
  dateHeader,
  items,
}) => {
  return (
    <div className="space-y-2">
      <p className="text-sm font-semibold text-gray-700 mt-4 mb-2">{dateHeader}</p>
      <div className="border rounded-lg overflow-hidden">
        {items.map((item, itemIndex) => (
          <TransactionHistoryItem key={itemIndex} {...item} />
        ))}
      </div>
    </div>
  );
};

export default TransactionHistoryGroup; 