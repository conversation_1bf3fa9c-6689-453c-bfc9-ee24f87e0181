import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface TransactionHistoryItemProps {
  title: string;
  subtitle: string;
  amount: string;
  date: string;
  type: 'received' | 'withdrawn' | 'refund' | 'pending';
  status?: string;
  from: string;
}

const TransactionHistoryItem: React.FC<TransactionHistoryItemProps> = ({
  title,
  subtitle,
  amount,
  date,
  type,
  status,
  from,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const getAmountColor = () => {
    // If transaction is pending, show yellow regardless of type
    if (type === 'pending' || status === 'pending') {
      return 'text-yellow-600';
    }
    if (type === 'received' || type === 'refund') {
      return 'text-green-600';
    } else if (type === 'withdrawn') {
      return 'text-red-600';
    }
    return 'text-gray-900';
  };

  const getAmountPrefix = () => {
    // For pending transactions, determine prefix based on amount sign
    if (type === 'pending' || status === 'pending') {
      return amount.startsWith('-') ? '-' : '+';
    }
    if (type === 'received' || type === 'refund') {
      return '+';
    } else if (type === 'withdrawn') {
      return '-';
    }
    return '';
  };

  return (
    <div className="border-b border-gray-200 last:border-b-0 py-3">
      <div
        className="flex items-center justify-between cursor-pointer hover:bg-gray-50 rounded-md p-2"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div>
          <p className="font-medium text-gray-900 text-sm">{title}</p>
          <div className="flex items-center space-x-2">
            <p className="text-xs text-gray-500">{date}</p>
            {(type === 'pending' || status === 'pending') && (
              <span className="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full font-medium">
                Pending
              </span>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <p className={`font-semibold text-sm ${getAmountColor()}`}>
            {getAmountPrefix()}{amount}
          </p>
          {isOpen ? (
            <ChevronUp size={16} className="text-gray-500" />
          ) : (
            <ChevronDown size={16} className="text-gray-500" />
          )}
        </div>
      </div>
      {isOpen && (
        <div className="px-2 pt-2 pb-1 text-xs text-gray-600 flex justify-between">
          <span>{subtitle}</span>
          <span>From {from}</span>
        </div>
      )}
    </div>
  );
};

export default TransactionHistoryItem; 