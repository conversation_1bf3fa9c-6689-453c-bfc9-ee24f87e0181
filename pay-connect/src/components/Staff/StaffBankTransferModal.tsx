import React, { useState, useEffect } from 'react';
import { X, Send, DollarSign, Building2, Wallet, ChevronDown, AlertCircle, Star } from 'lucide-react';
import { transferToStaffBank, getStaffBankAccountsById, StaffBankAccount } from '../../services/staff';
import { getConnectedAccounts, ConnectedBankAccount } from '../../services/plaid';
import { getWallet } from '../../services/wallet';

interface StaffBankTransferModalProps {
  isOpen: boolean;
  onClose: () => void;
  staffId: string;
  staffName: string;
  onSuccess: () => void;
}

const StaffBankTransferModal: React.FC<StaffBankTransferModalProps> = ({
  isOpen,
  onClose,
  staffId,
  staffName,
  onSuccess
}) => {
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [pin, setPin] = useState('');
  const [paymentSource, setPaymentSource] = useState<'wallet' | 'bank'>('wallet');
  const [selectedBankAccount, setSelectedBankAccount] = useState<ConnectedBankAccount | null>(null);
  const [showBankDropdown, setShowBankDropdown] = useState(false);
  const [connectedAccounts, setConnectedAccounts] = useState<ConnectedBankAccount[]>([]);
  const [staffBankAccounts, setStaffBankAccounts] = useState<StaffBankAccount[]>([]);
  const [selectedStaffBankAccount, setSelectedStaffBankAccount] = useState<StaffBankAccount | null>(null);
  const [showStaffBankDropdown, setShowStaffBankDropdown] = useState(false);
  const [walletBalance, setWalletBalance] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loadingAccounts, setLoadingAccounts] = useState(false);
  const [loadingStaffAccounts, setLoadingStaffAccounts] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [statusMessage, setStatusMessage] = useState<{
    type: 'success' | 'error' | 'info' | null;
    message: string | null;
  }>({ type: null, message: null });
  
  // Transfer status tracking
  const [transferStatus, setTransferStatus] = useState<{
    status: 'idle' | 'processing' | 'completed' | 'failed';
    transactionId?: number;
    details?: string;
  }>({ status: 'idle' });

  useEffect(() => {
    if (isOpen) {
      fetchWalletBalance();
      fetchBankAccounts();
      fetchStaffBankAccounts();
    }
  }, [isOpen, staffId]);

  const fetchWalletBalance = async () => {
    try {
      const wallet = await getWallet();
      setWalletBalance(wallet.balance);
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
    }
  };

  const fetchBankAccounts = async () => {
    try {
      setLoadingAccounts(true);
      const accounts = await getConnectedAccounts();
      setConnectedAccounts(accounts);
      if (accounts.length > 0) {
        const primaryAccount = accounts.find(acc => acc.is_primary) || accounts[0];
        setSelectedBankAccount(primaryAccount);
      }
    } catch (error) {
      console.error('Error fetching bank accounts:', error);
    } finally {
      setLoadingAccounts(false);
    }
  };
  
  const fetchStaffBankAccounts = async () => {
    if (!staffId) return;
    
    try {
      setLoadingStaffAccounts(true);
      setError(null);
      const accounts = await getStaffBankAccountsById(staffId);
      
      setStaffBankAccounts(accounts);
      
      // Select the primary account by default
      if (accounts.length > 0) {
        const primaryAccount = accounts.find(acc => acc.isPrimary) || accounts[0];
        setSelectedStaffBankAccount(primaryAccount);
      } else {
        setError("No bank accounts found for this staff member. Please add a bank account first.");
      }
    } catch (err) {
      console.error('Error fetching staff bank accounts:', err);
      setError("Failed to fetch staff bank accounts");
    } finally {
      setLoadingStaffAccounts(false);
    }
  };

  // Function to poll for transfer status updates
  const pollTransferStatus = async (transactionId: number) => {
    try {
      // In a real implementation, this would call an API to get the latest status
      // For now, we'll simulate status updates with timeouts
      
      // Set initial processing status
      setTransferStatus({
        status: 'processing',
        transactionId,
        details: 'Transfer initiated'
      });
      
      // Simulate status updates
      if (paymentSource === 'wallet') {
        // Wallet transfers are faster
        await new Promise(resolve => setTimeout(resolve, 1500));
        setTransferStatus({
          status: 'processing',
          transactionId,
          details: 'Processing wallet transfer'
        });
        
        await new Promise(resolve => setTimeout(resolve, 1500));
        setTransferStatus({
          status: 'completed',
          transactionId,
          details: 'Transfer completed successfully'
        });
        
        // Show success message
        setStatusMessage({
          type: 'success',
          message: `Transfer of ${formatCurrency(parseFloat(amount))} completed successfully`
        });
        
        // Notify parent component of success
        onSuccess();
        
        // Close modal after a delay
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        // Bank transfers take longer
        await new Promise(resolve => setTimeout(resolve, 1500));
        setTransferStatus({
          status: 'processing',
          transactionId,
          details: 'Transfer request submitted to bank'
        });
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        setTransferStatus({
          status: 'processing',
          transactionId,
          details: 'Bank processing transfer (1-3 business days)'
        });
        
        // For demo purposes, we'll complete it after a short delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        setTransferStatus({
          status: 'completed',
          transactionId,
          details: 'Transfer initiated successfully. Final processing may take 1-3 business days.'
        });
        
        // Show success message
        setStatusMessage({
          type: 'success',
          message: `Transfer of ${formatCurrency(parseFloat(amount))} initiated successfully`
        });
        
        // Notify parent component of success
        onSuccess();
        
        // Close modal after a delay
        setTimeout(() => {
          onClose();
        }, 2000);
      }
    } catch (error) {
      console.error('Error polling transfer status:', error);
      setTransferStatus({
        status: 'failed',
        transactionId,
        details: 'Failed to get transfer status updates'
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset status message and transfer status
    setStatusMessage({ type: null, message: null });
    setTransferStatus({ status: 'idle' });
    
    // Validate amount
    const transferAmount = parseFloat(amount);
    if (isNaN(transferAmount) || transferAmount <= 0) {
      setStatusMessage({
        type: 'error',
        message: 'Please enter a valid amount'
      });
      return;
    }
    
    if (transferAmount < 1 || transferAmount > 10000) {
      setStatusMessage({
        type: 'error',
        message: 'Amount must be between $1 and $10,000'
      });
      return;
    }

    // Validate staff bank account
    if (!selectedStaffBankAccount) {
      setStatusMessage({
        type: 'error',
        message: 'Please select a destination bank account for the staff member'
      });
      return;
    }
    
    // Check if the bank account is active
    if (selectedStaffBankAccount.status === 'inactive') {
      setStatusMessage({
        type: 'error',
        message: 'Cannot transfer to an inactive bank account. Please select an active account.'
      });
      return;
    }

    // Validate payment source
    if (paymentSource === 'wallet') {
      if (transferAmount > walletBalance) {
        setStatusMessage({
          type: 'error',
          message: `Insufficient wallet balance. Available: ${formatCurrency(walletBalance)}`
        });
        return;
      }
      if (!pin || pin.length < 4) {
        setStatusMessage({
          type: 'error',
          message: 'Please enter your wallet PIN'
        });
        return;
      }
    } else {
      if (!selectedBankAccount) {
        setStatusMessage({
          type: 'error',
          message: 'Please select a source bank account'
        });
        return;
      }
      if (transferAmount > (selectedBankAccount.balance.available || 0)) {
        setStatusMessage({
          type: 'error',
          message: `Insufficient bank balance. Available: ${formatCurrency(selectedBankAccount.balance.available || 0)}`
        });
        return;
      }
    }

    setIsSubmitting(true);
    
    // Update transfer status to processing
    setTransferStatus({
      status: 'processing',
      details: 'Initiating transfer...'
    });

    try {
      // Include the selected staff bank account ID in the transfer request
      const result = await transferToStaffBank(
        staffId,
        transferAmount,
        description || `Payment to ${staffName}'s ${selectedStaffBankAccount.bankName} account`,
        paymentSource === 'wallet' ? pin : '',
        paymentSource,
        paymentSource === 'bank' ? selectedBankAccount?.id : undefined,
        selectedStaffBankAccount.id.toString() // Pass the selected staff bank account ID
      );
      
      if (result.success) {
        // Start polling for status updates
        if (result.transactionId) {
          pollTransferStatus(result.transactionId);
        } else {
          // If no transaction ID, just show success
          setTransferStatus({
            status: 'completed',
            details: 'Transfer completed successfully'
          });
          
          // Show success message
          setStatusMessage({
            type: 'success',
            message: result.message || 'Transfer completed successfully'
          });
          
          // Reset form
          setAmount('');
          setDescription('');
          setPin('');
          
          // Notify parent component of success
          onSuccess();
          
          // Close modal after a short delay to show success message
          setTimeout(() => {
            onClose();
          }, 2000);
        }
      } else {
        // Update transfer status to failed
        setTransferStatus({
          status: 'failed',
          details: result.message || 'Transfer failed',
          transactionId: result.transactionId
        });
        
        setStatusMessage({
          type: 'error',
          message: result.message || 'Failed to transfer funds'
        });
      }
    } catch (error) {
      console.error('Transfer error:', error);
      
      // Extract error message from API response if available
      let errorMessage = 'Failed to transfer funds. Please try again.';
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: { message?: string } } };
        errorMessage = axiosError.response?.data?.message || errorMessage;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      // Update transfer status to failed
      setTransferStatus({
        status: 'failed',
        details: errorMessage
      });
      
      setStatusMessage({
        type: 'error',
        message: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Function to retry a failed transfer
  const handleRetryTransfer = async () => {
    if (!transferStatus.transactionId) {
      // If no transaction ID, just restart the form submission
      handleSubmit(new Event('submit') as React.FormEvent);
      return;
    }
    
    try {
      setIsSubmitting(true);
      setStatusMessage({ type: 'info', message: 'Retrying transfer...' });
      
      // Use the retryTransaction function from transactionStatus service
      const { retryTransaction } = await import('../../services/transactionStatus');
      const updatedStatus = await retryTransaction(transferStatus.transactionId);
      
      // Update the transfer status
      setTransferStatus({
        status: updatedStatus.status as 'idle' | 'processing' | 'completed' | 'failed',
        transactionId: parseInt(updatedStatus.transactionId),
        details: updatedStatus.details
      });
      
      // If the retry was successful, start polling for status updates
      if (updatedStatus.status === 'pending' || updatedStatus.status === 'processing') {
        pollTransferStatus(updatedStatus.transactionId);
      } else if (updatedStatus.status === 'completed') {
        setStatusMessage({
          type: 'success',
          message: 'Transfer completed successfully'
        });
        
        // Notify parent component of success
        onSuccess();
        
        // Close modal after a short delay to show success message
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setStatusMessage({
          type: 'error',
          message: updatedStatus.details || 'Failed to retry transfer'
        });
      }
    } catch (error) {
      console.error('Error retrying transfer:', error);
      
      let errorMessage = 'Failed to retry transfer. Please try again.';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      setStatusMessage({
        type: 'error',
        message: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Building2 className="text-blue-600" size={20} />
            <h3 className="text-lg font-semibold text-gray-900">
              Transfer to {staffName}
            </h3>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X size={20} />
          </button>
        </div>

        {/* Status message */}
        {statusMessage.type && (
          <div className={`mb-4 p-3 rounded-lg ${
            statusMessage.type === 'success' ? 'bg-green-100 text-green-800' : 
            statusMessage.type === 'info' ? 'bg-blue-100 text-blue-800' : 
            'bg-red-100 text-red-800'
          }`}>
            <div className="flex items-center">
              {statusMessage.type === 'success' ? (
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : statusMessage.type === 'info' ? (
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              ) : (
                <AlertCircle size={20} className="mr-2" />
              )}
              <span>{statusMessage.message}</span>
            </div>
          </div>
        )}
        
        {/* Transfer Status Tracker */}
        {transferStatus.status !== 'idle' && (
          <div className="mb-4 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Transfer Status</h4>
              {transferStatus.transactionId && (
                <span className="text-xs text-gray-500">ID: {transferStatus.transactionId}</span>
              )}
            </div>
            
            <div className="relative">
              {/* Status Bar */}
              <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
                <div 
                  className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center ${
                    transferStatus.status === 'completed' ? 'bg-green-500' : 
                    transferStatus.status === 'failed' ? 'bg-red-500' : 
                    'bg-blue-500'
                  }`} 
                  style={{ 
                    width: transferStatus.status === 'completed' ? '100%' : 
                           transferStatus.status === 'failed' ? '100%' : 
                           '60%',
                    transition: 'width 1s ease-in-out'
                  }}
                ></div>
              </div>
              
              {/* Status Details */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`rounded-full w-8 h-8 flex items-center justify-center mr-3 ${
                    transferStatus.status === 'completed' ? 'bg-green-100 text-green-600' : 
                    transferStatus.status === 'failed' ? 'bg-red-100 text-red-600' : 
                    'bg-blue-100 text-blue-600'
                  }`}>
                    {transferStatus.status === 'completed' ? (
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : transferStatus.status === 'failed' ? (
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    )}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {transferStatus.status === 'completed' ? 'Transfer Completed' : 
                       transferStatus.status === 'failed' ? 'Transfer Failed' : 
                       'Processing Transfer'}
                    </div>
                    <div className="text-xs text-gray-500">{transferStatus.details}</div>
                  </div>
                </div>
                
                {/* Retry button for failed transfers */}
                {transferStatus.status === 'failed' && (
                  <button
                    type="button"
                    onClick={handleRetryTransfer}
                    disabled={isSubmitting}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center space-x-1"
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="w-4 h-4 animate-spin mr-1" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Retrying...</span>
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                        </svg>
                        <span>Retry</span>
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <p className="text-sm text-blue-800">
            💡 Transfer funds to {staffName}'s registered bank account from your wallet or bank.
          </p>
        </div>
        
        {/* Staff Bank Account Selection */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Destination Bank Account
          </label>
          {loadingStaffAccounts ? (
            <div className="bg-gray-50 rounded-lg p-3 text-center">
              <p className="text-sm text-gray-500">Loading staff bank accounts...</p>
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-center">
              <div className="flex items-center justify-center text-red-600 mb-1">
                <AlertCircle size={16} className="mr-1" />
                <p className="text-sm font-medium">Error</p>
              </div>
              <p className="text-sm text-red-600">{error}</p>
            </div>
          ) : staffBankAccounts.length === 0 ? (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center">
              <p className="text-sm text-yellow-700">No bank accounts found for this staff member. Please add a bank account first.</p>
            </div>
          ) : (
            <div className="relative">
              <button
                type="button"
                onClick={() => setShowStaffBankDropdown(!showStaffBankDropdown)}
                className="w-full bg-white border border-gray-300 rounded-lg p-3 flex items-center justify-between hover:border-blue-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-colors"
                disabled={staffBankAccounts.length === 0}
              >
                {selectedStaffBankAccount ? (
                  <div className="flex items-center space-x-3">
                    <Building2 size={16} className="text-gray-400" />
                    <div className="text-left">
                      <div className="font-medium text-sm flex items-center">
                        {selectedStaffBankAccount.bankName}
                        {selectedStaffBankAccount.isPrimary && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800">
                            <Star size={10} className="mr-1" /> Primary
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500">
                        {selectedStaffBankAccount.accountType} • {selectedStaffBankAccount.accountNumber}
                      </div>
                    </div>
                  </div>
                ) : (
                  <span className="text-gray-500">Select staff bank account</span>
                )}
                <ChevronDown size={16} className={`text-gray-400 transition-transform ${showStaffBankDropdown ? 'rotate-180' : ''}`} />
              </button>

              {showStaffBankDropdown && staffBankAccounts.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-40 overflow-y-auto">
                  {staffBankAccounts.map((account) => (
                    <button
                      key={account.id}
                      type="button"
                      onClick={() => {
                        setSelectedStaffBankAccount(account);
                        setShowStaffBankDropdown(false);
                      }}
                      className="w-full p-3 text-left hover:bg-gray-50 flex items-center space-x-3"
                    >
                      <Building2 size={16} className="text-gray-400" />
                      <div>
                        <div className="font-medium text-sm flex items-center">
                          {account.bankName}
                          {account.isPrimary && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800">
                              <Star size={10} className="mr-1" /> Primary
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-gray-500">
                          {account.accountType} • {account.accountNumber} • {account.accountHolderName}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Payment Source Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Payment Source
            </label>
            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => {
                  setPaymentSource('wallet');
                  // Clear any status messages when switching payment source
                  setStatusMessage({ type: null, message: null });
                }}
                className={`p-3 rounded-lg border-2 transition-colors ${
                  paymentSource === 'wallet'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                disabled={walletBalance <= 0}
                title={walletBalance <= 0 ? 'Insufficient wallet balance' : 'Pay from wallet'}
              >
                <div className="flex items-center space-x-2">
                  <Wallet size={20} className={paymentSource === 'wallet' ? 'text-blue-600' : 'text-gray-400'} />
                  <div className="text-left">
                    <div className="font-medium text-sm">Wallet</div>
                    <div className={`text-xs ${walletBalance <= 0 ? 'text-red-500 font-medium' : 'text-gray-500'}`}>
                      {formatCurrency(walletBalance)}
                    </div>
                  </div>
                </div>
              </button>

              <button
                type="button"
                onClick={() => {
                  setPaymentSource('bank');
                  // Clear any status messages when switching payment source
                  setStatusMessage({ type: null, message: null });
                }}
                className={`p-3 rounded-lg border-2 transition-colors ${
                  paymentSource === 'bank'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                disabled={connectedAccounts.length === 0}
                title={connectedAccounts.length === 0 ? 'No bank accounts connected' : 'Pay from bank account'}
              >
                <div className="flex items-center space-x-2">
                  <Building2 size={20} className={paymentSource === 'bank' ? 'text-blue-600' : 'text-gray-400'} />
                  <div className="text-left">
                    <div className="font-medium text-sm">Bank</div>
                    <div className="text-xs text-gray-500">
                      {selectedBankAccount 
                        ? formatCurrency(selectedBankAccount.balance.available || 0) 
                        : connectedAccounts.length === 0 
                          ? 'No accounts' 
                          : 'Select account'
                      }
                    </div>
                  </div>
                </div>
              </button>
            </div>
            
            {/* Payment source guidance */}
            <div className="mt-2 text-xs text-gray-500">
              {paymentSource === 'wallet' ? (
                <div className="flex items-center">
                  <Wallet size={12} className="mr-1" />
                  <span>Wallet transfers are processed immediately</span>
                </div>
              ) : (
                <div className="flex items-center">
                  <Building2 size={12} className="mr-1" />
                  <span>Bank transfers may take 1-3 business days to process</span>
                </div>
              )}
            </div>
          </div>

          {/* Bank Account Selection (only show if bank is selected) */}
          {paymentSource === 'bank' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Bank Account
              </label>
              {loadingAccounts ? (
                <div className="bg-gray-50 rounded-lg p-3 text-center">
                  <p className="text-sm text-gray-500">Loading accounts...</p>
                </div>
              ) : connectedAccounts.length === 0 ? (
                <div className="bg-red-50 rounded-lg p-3 text-center">
                  <p className="text-sm text-red-600">No bank accounts connected</p>
                </div>
              ) : (
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => setShowBankDropdown(!showBankDropdown)}
                    className="w-full bg-white border border-gray-300 rounded-lg p-3 flex items-center justify-between hover:border-blue-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-colors"
                  >
                    {selectedBankAccount ? (
                      <div className="flex items-center space-x-3">
                        <Building2 size={16} className="text-gray-400" />
                        <div className="text-left">
                          <div className="font-medium text-sm">{selectedBankAccount.name}</div>
                          <div className="text-xs text-gray-500">
                            {selectedBankAccount.institution_name} • ••••{selectedBankAccount.mask}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-500">Select account</span>
                    )}
                    <ChevronDown size={16} className={`text-gray-400 transition-transform ${showBankDropdown ? 'rotate-180' : ''}`} />
                  </button>

                  {showBankDropdown && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-40 overflow-y-auto">
                      {connectedAccounts.map((account) => (
                        <button
                          key={account.id}
                          type="button"
                          onClick={() => {
                            setSelectedBankAccount(account);
                            setShowBankDropdown(false);
                          }}
                          className="w-full p-3 text-left hover:bg-gray-50 flex items-center space-x-3"
                        >
                          <Building2 size={16} className="text-gray-400" />
                          <div>
                            <div className="font-medium text-sm">{account.name}</div>
                            <div className="text-xs text-gray-500">
                              {account.institution_name} • ••••{account.mask} • {formatCurrency(account.balance.available || 0)}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Amount input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Transfer Amount
            </label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <input
                type="number"
                required
                min="1"
                max="10000"
                step="0.01"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="0.00"
              />
            </div>
            
            <div className="flex flex-wrap gap-2 mt-2">
              {[50, 100, 250, 500, 1000].map((quickAmount) => (
                <button
                  key={quickAmount}
                  type="button"
                  onClick={() => setAmount(quickAmount.toString())}
                  className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  ${quickAmount}
                </button>
              ))}
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Payment Description
            </label>
            <input
              type="text"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., Salary payment, Bonus, etc."
            />
          </div>

          {/* Wallet PIN (only show if wallet is selected) */}
          {paymentSource === 'wallet' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Wallet PIN
              </label>
              <input
                type="password"
                required
                value={pin}
                onChange={(e) => setPin(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your wallet PIN"
                minLength={4}
              />
            </div>
          )}

          {/* Action buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={
                isSubmitting || 
                !amount || 
                !selectedStaffBankAccount || 
                (paymentSource === 'wallet' && !pin) || 
                (paymentSource === 'bank' && !selectedBankAccount)
              }
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center justify-center space-x-2"
            >
              <Send size={16} />
              <span>{isSubmitting ? 'Processing...' : `Transfer from ${paymentSource === 'wallet' ? 'Wallet' : 'Bank'}`}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StaffBankTransferModal;