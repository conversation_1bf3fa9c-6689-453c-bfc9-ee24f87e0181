import React, { useState, useEffect } from 'react';
import { Building2, Eye, EyeOff, Calendar, User, CreditCard, Star, RefreshCw, CheckCircle, AlertCircle, ToggleLeft, ToggleRight } from 'lucide-react';
import { getStaffBankAccounts, StaffBankAccount, setPrimaryBankAccount, updateBankAccountStatus } from '../../services/staff';
import PaymentLoader from '../common/PaymentLoader';

// Mock function to simulate unmasking account numbers
// In a real implementation, this would be a secure API call to get the full account number
const unmaskAccountNumber = (maskedNumber: string): string => {
  // This is just a simulation - in a real app, you would make an API call
  // to get the actual unmasked account number with proper authentication
  if (maskedNumber.startsWith('****')) {
    const lastFour = maskedNumber.slice(-4);
    return `**********${lastFour}`;
  }
  return maskedNumber;
};

const StaffBankAccounts: React.FC = () => {
  const [accounts, setAccounts] = useState<StaffBankAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [visibleAccounts, setVisibleAccounts] = useState<Set<number>>(new Set());
  const [refreshing, setRefreshing] = useState(false);
  const [unmaskedAccounts, setUnmaskedAccounts] = useState<Record<number, string>>({});
  const [statusMessage, setStatusMessage] = useState<{
    type: 'success' | 'error' | null;
    message: string | null;
  }>({ type: null, message: null });
  const [updatingPrimary, setUpdatingPrimary] = useState<number | null>(null);
  const [updatingStatus, setUpdatingStatus] = useState<number | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [accountToToggle, setAccountToToggle] = useState<{account: StaffBankAccount, newStatus: 'active' | 'inactive'} | null>(null);

  useEffect(() => {
    fetchBankAccounts();
  }, []);

  const fetchBankAccounts = async () => {
    try {
      setLoading(true);
      const data = await getStaffBankAccounts();
      setAccounts(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch staff bank accounts');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const refreshAccounts = () => {
    setRefreshing(true);
    fetchBankAccounts();
  };

  const toggleAccountVisibility = (accountId: number, maskedNumber: string) => {
    const newVisible = new Set(visibleAccounts);
    
    if (newVisible.has(accountId)) {
      // Hide the account number
      newVisible.delete(accountId);
    } else {
      // Show the unmasked account number
      newVisible.add(accountId);
      
      // If we don't have the unmasked version yet, generate it
      if (!unmaskedAccounts[accountId]) {
        const unmaskedNumber = unmaskAccountNumber(maskedNumber);
        setUnmaskedAccounts(prev => ({
          ...prev,
          [accountId]: unmaskedNumber
        }));
      }
    }
    
    setVisibleAccounts(newVisible);
  };

  const handleSetPrimary = async (account: StaffBankAccount) => {
    // Don't do anything if this account is already primary
    if (account.isPrimary) return;
    
    try {
      setUpdatingPrimary(account.id);
      setStatusMessage({ type: null, message: null });
      
      const result = await setPrimaryBankAccount(account.staffId, account.id);
      
      if (result.success) {
        // Update the accounts list to reflect the new primary account
        setAccounts(prevAccounts => {
          return prevAccounts.map(acc => ({
            ...acc,
            isPrimary: acc.id === account.id ? true : 
                       acc.staffId === account.staffId ? false : acc.isPrimary
          }));
        });
        
        setStatusMessage({
          type: 'success',
          message: result.message || `Set ${account.bankName} as primary account for ${account.staffName}`
        });
      } else {
        setStatusMessage({
          type: 'error',
          message: result.message || 'Failed to set primary account'
        });
      }
    } catch (err) {
      console.error('Error setting primary account:', err);
      setStatusMessage({
        type: 'error',
        message: 'An unexpected error occurred while setting primary account'
      });
    } finally {
      setUpdatingPrimary(null);
    }
  };
  
  const handleToggleStatus = async (account: StaffBankAccount) => {
    // Don't allow deactivating primary accounts
    if (account.isPrimary) return;
    
    // Determine the new status (toggle current status)
    const newStatus = account.status === 'inactive' ? 'active' : 'inactive';
    
    // Show confirmation dialog for deactivation
    if (newStatus === 'inactive') {
      setAccountToToggle({ account, newStatus });
      setShowConfirmDialog(true);
      return;
    }
    
    // For activation, proceed directly
    await updateAccountStatus(account, newStatus);
  };
  
  const updateAccountStatus = async (account: StaffBankAccount, newStatus: 'active' | 'inactive') => {
    try {
      setUpdatingStatus(account.id);
      setStatusMessage({ type: null, message: null });
      
      const result = await updateBankAccountStatus(account.id, newStatus);
      
      if (result.success) {
        // Update the accounts list to reflect the new status
        setAccounts(prevAccounts => {
          return prevAccounts.map(acc => ({
            ...acc,
            status: acc.id === account.id ? newStatus : acc.status
          }));
        });
        
        setStatusMessage({
          type: 'success',
          message: result.message || `Bank account ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`
        });
      } else {
        setStatusMessage({
          type: 'error',
          message: result.message || `Failed to ${newStatus === 'active' ? 'activate' : 'deactivate'} bank account`
        });
      }
    } catch (err) {
      console.error(`Error ${newStatus === 'active' ? 'activating' : 'deactivating'} bank account:`, err);
      setStatusMessage({
        type: 'error',
        message: `An unexpected error occurred while ${newStatus === 'active' ? 'activating' : 'deactivating'} the bank account`
      });
    } finally {
      setUpdatingStatus(null);
      setAccountToToggle(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex items-center justify-center py-12">
          <PaymentLoader
            type="setup"
            message="Loading staff bank accounts..."
            size="medium"
            showQuotes={true}
          />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  // Confirmation dialog for deactivating bank accounts
  const ConfirmationDialog = () => {
    if (!accountToToggle) return null;
    
    const { account, newStatus } = accountToToggle;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Confirm Account Deactivation
          </h3>
          
          <p className="text-gray-600 mb-4">
            Are you sure you want to deactivate the bank account <span className="font-medium">{account.bankName}</span> for <span className="font-medium">{account.staffName}</span>?
          </p>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
            <div className="flex items-center text-yellow-800">
              <AlertCircle size={16} className="mr-2" />
              <p className="text-sm">
                Deactivated accounts will not be available for transfers. This action can be reversed later.
              </p>
            </div>
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowConfirmDialog(false)}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                setShowConfirmDialog(false);
                updateAccountStatus(account, newStatus);
              }}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Deactivate
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      {showConfirmDialog && <ConfirmationDialog />}
      
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Building2 className="text-blue-600" size={24} />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Staff Bank Accounts</h2>
            <p className="text-sm text-gray-500">Manage your staff members' bank accounts</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button 
            onClick={refreshAccounts}
            className="p-2 rounded-full bg-blue-50 hover:bg-blue-100 text-blue-600 transition"
            disabled={refreshing}
            title="Refresh accounts"
          >
            <RefreshCw size={16} className={refreshing ? "animate-spin" : ""} />
          </button>
          <div className="bg-blue-50 px-3 py-1 rounded-full">
            <span className="text-sm font-medium text-blue-700">{accounts.length} Accounts</span>
          </div>
        </div>
      </div>

      {/* Status message */}
      {statusMessage.type && (
        <div className={`mb-4 p-3 rounded-lg ${
          statusMessage.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          <div className="flex items-center">
            {statusMessage.type === 'success' ? (
              <CheckCircle size={20} className="mr-2" />
            ) : (
              <AlertCircle size={20} className="mr-2" />
            )}
            <span>{statusMessage.message}</span>
          </div>
        </div>
      )}

      {accounts.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <Building2 className="text-gray-400 w-8 h-8" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Bank Accounts</h3>
          <p className="text-gray-500">No staff bank accounts have been added yet.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {accounts.map((account) => (
            <div
              key={account.id}
              className={`border rounded-lg p-4 hover:shadow-md transition-shadow ${
                account.isPrimary ? 'border-blue-300 bg-blue-50' : 'border-gray-200'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    account.isPrimary ? 'bg-blue-200' : 'bg-blue-100'
                  }`}>
                    <User className="text-blue-600 w-6 h-6" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-semibold text-gray-900">{account.staffName}</h4>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                        account.status === 'inactive' 
                          ? 'bg-gray-100 text-gray-800' 
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {account.status === 'inactive' ? 'Inactive' : 'Active'}
                      </span>
                      {account.isPrimary && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                          <Star size={12} className="mr-1" /> Primary
                        </span>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <Building2 size={14} />
                        <span>{account.bankName}</span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <CreditCard size={14} />
                        <span className="capitalize">{account.accountType}</span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Calendar size={14} />
                        <span>Added {formatDate(account.createdAt)}</span>
                      </div>
                    </div>
                    
                    <div className="mt-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">Account:</span>
                        <span className="text-sm font-mono">
                          {visibleAccounts.has(account.id)
                            ? unmaskedAccounts[account.id] || account.accountNumber
                            : account.accountNumber
                          }
                        </span>
                        <button
                          onClick={() => toggleAccountVisibility(account.id, account.accountNumber)}
                          className="text-gray-400 hover:text-gray-600"
                          title={visibleAccounts.has(account.id) ? "Hide account number" : "Show full account number"}
                        >
                          {visibleAccounts.has(account.id) ? (
                            <EyeOff size={14} />
                          ) : (
                            <Eye size={14} />
                          )}
                        </button>
                      </div>
                      
                      <div className="text-sm text-gray-500 mt-1">
                        Account Holder: {account.accountHolderName}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="ml-4 flex flex-col space-y-2">
                  {!account.isPrimary && (
                    <button
                      onClick={() => handleSetPrimary(account)}
                      disabled={updatingPrimary === account.id}
                      className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                    >
                      {updatingPrimary === account.id ? (
                        <>
                          <RefreshCw size={14} className="animate-spin" />
                          <span>Setting...</span>
                        </>
                      ) : (
                        <>
                          <Star size={14} />
                          <span>Set as Primary</span>
                        </>
                      )}
                    </button>
                  )}
                  <button
                    onClick={() => handleToggleStatus(account)}
                    disabled={updatingStatus === account.id || account.isPrimary}
                    className={`px-3 py-1 text-sm ${
                      account.status === 'inactive'
                        ? 'bg-green-600 hover:bg-green-700'
                        : 'bg-gray-600 hover:bg-gray-700'
                    } text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1`}
                    title={account.isPrimary ? "Cannot deactivate primary account" : ""}
                  >
                    {updatingStatus === account.id ? (
                      <>
                        <RefreshCw size={14} className="animate-spin" />
                        <span>Updating...</span>
                      </>
                    ) : account.status === 'inactive' ? (
                      <>
                        <ToggleRight size={14} />
                        <span>Activate</span>
                      </>
                    ) : (
                      <>
                        <ToggleLeft size={14} />
                        <span>Deactivate</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default StaffBankAccounts;