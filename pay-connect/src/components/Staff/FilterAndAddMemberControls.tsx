import React from 'react';
import { Download } from 'lucide-react';

interface FilterAndAddMemberControlsProps {
  className?: string;
  onFilterChange: (role: string) => void;
  onImportUsers: () => void;
  currentFilter: string;
}

const FilterAndAddMemberControls: React.FC<FilterAndAddMemberControlsProps> = ({
  className,
  onFilterChange,
  onImportUsers,
  currentFilter
}) => {
  return (
    <div className={`flex items-center justify-between ${className}`}>
      <div className="flex items-center space-x-4">
        <label htmlFor="filter" className="text-gray-700 text-sm font-medium">Filter:</label>
        <select
          id="filter"
          className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          value={currentFilter}
          onChange={(e) => onFilterChange(e.target.value)}
        >
          <option value="All">All</option>
          <option value="Office Staff">Office Staff</option>
          <option value="Referee">Referee</option>
          <option value="Other Staff">Other Staff</option>
        </select>
      </div>

      <div className="flex items-center space-x-3">
        <button
          onClick={onImportUsers}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
         
          Import Users
           <Download className="ml-3  h-4 w-4" aria-hidden="true" />
        </button>

      </div>
    </div>
  );
};

export default FilterAndAddMemberControls; 