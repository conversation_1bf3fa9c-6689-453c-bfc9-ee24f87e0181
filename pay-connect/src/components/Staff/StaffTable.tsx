import React, { useState, useEffect } from 'react';
import StaffTableRow from './StaffTableRow';
import { getStaffMembers, StaffMember } from '../../services/staff';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import EmptyState from '../common/EmptyState';
import PaymentLoader from '../common/PaymentLoader';
import AddBankAccountModal from './AddBankAccountModal';
import StaffBankTransferModal from './StaffBankTransferModal';

interface StaffTableProps {
  className?: string;
  search: string;
  roleFilter: string;
  onDelete: (id: string) => void;
}

const StaffTable: React.FC<StaffTableProps> = ({
  className,
  search,
  roleFilter,
  onDelete,
}) => {
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showAddBankModal, setShowAddBankModal] = useState(false);
  const [showTransferModal, setShowTransferModal] = useState(false);

  const [selectedStaffForBank, setSelectedStaffForBank] = useState<StaffMember | null>(null);
  const [selectedStaffForTransfer, setSelectedStaffForTransfer] = useState<StaffMember | null>(null);

  const handleAddBankAccount = (staff: StaffMember) => {
    setSelectedStaffForBank(staff);
    setShowAddBankModal(true);
  };

  const handleBankTransfer = (staff: StaffMember) => {
    setSelectedStaffForTransfer(staff);
    setShowTransferModal(true);
  };

  const handleBankSuccess = () => {
    // Refresh staff data after bank operations
    refreshData();
    setSelectedStaffForBank(null);
    setSelectedStaffForTransfer(null);
  };

  useEffect(() => {
    fetchStaffMembers();
  }, [search, roleFilter, currentPage]);

  const fetchStaffMembers = async () => {
    try {
      setLoading(true);
      
      // Only add a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Fetch staff members with pagination and filtering
      const { data, totalPages: newTotalPages } = await getStaffMembers({
        search,
        role: roleFilter !== 'All' ? (roleFilter as StaffMember['role']) : undefined,
        page: currentPage,
        limit: 10
      });
      
      setStaffMembers(data);
      setTotalPages(newTotalPages);
      setError(null);
    } catch (err) {
      console.error('Error fetching staff members:', err);
      setError('Failed to fetch staff members. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Function to refresh data after operations
  const refreshData = () => {
    fetchStaffMembers();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <PaymentLoader
          type="setup"
          message="Loading staff members..."
          size="medium"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        {error}
      </div>
    );
  }

  const startIndex = (currentPage - 1) * 10;
  const endIndex = Math.min(startIndex + 10, staffMembers.length);
  const currentStaffMembers = staffMembers.slice(startIndex, endIndex);

  // Check if we should show empty state
  const hasSearchOrFilter = search.trim() !== '' || roleFilter !== 'All';
  const showEmptyState = staffMembers.length === 0;

  if (showEmptyState) {
    return (
      <div className={`rounded-md border border-gray-200 ${className}`}>
        <EmptyState
          type={hasSearchOrFilter ? 'search' : 'staff'}
          title={hasSearchOrFilter ? 'No staff members found' : undefined}
          description={hasSearchOrFilter ? 'Try adjusting your search criteria or filters.' : undefined}
        />
      </div>
    );
  }


  return (
    <>
      <div className={`overflow-x-auto rounded-md ${className}`}>
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-[#404040]">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                <input type="checkbox" className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out" />
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Staff Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Email
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Contact
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Role
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentStaffMembers.map((staff) => (
              <StaffTableRow 
                key={staff.id} 
                staff={staff} 
                onDelete={onDelete}
                onAddBankAccount={handleAddBankAccount}
                onBankTransfer={handleBankTransfer}
              />
            ))}
          </tbody>
        </table>
        <nav
          className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
          aria-label="Pagination"
        >
          <div className="flex-1 flex justify-between sm:justify-end items-center">
            <p className="text-sm text-gray-700 mr-4">
              Showing <span className="font-medium">{startIndex + 1}</span> to <span className="font-medium">{endIndex}</span> of {' '}
              <span className="font-medium">{staffMembers.length}</span> results
            </p>
            <div className="flex">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Previous</span>
                <ChevronLeft className="h-5 w-5" aria-hidden="true" />
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Next</span>
                <ChevronRight className="h-5 w-5" aria-hidden="true" />
              </button>
            </div>
          </div>
        </nav>
      </div>
      <AddBankAccountModal
        isOpen={showAddBankModal}
        onClose={() => setShowAddBankModal(false)}
        staffId={selectedStaffForBank?.id || ''}
        staffName={selectedStaffForBank?.name || ''}
        onSuccess={handleBankSuccess}
      />
      <StaffBankTransferModal
        isOpen={showTransferModal}
        onClose={() => setShowTransferModal(false)}
        staffId={selectedStaffForTransfer?.id || ''}
        staffName={selectedStaffForTransfer?.name || ''}
        onSuccess={handleBankSuccess}
      />
    </>
  );
};

export default StaffTable; 
