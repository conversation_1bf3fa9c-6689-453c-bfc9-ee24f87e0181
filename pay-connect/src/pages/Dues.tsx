import React, { useState, useEffect } from 'react';
import SummaryCard from '../components/Dues/SummaryCard';
import SearchBar from '../components/Dues/SearchBar';
import FilterAndExportControls from '../components/Dues/FilterAndExportControls';
import DuesTable from '../components/Dues/DuesTable';
import PlatformDuesTable from '../components/Dues/PlatformDuesTable';
import PaymentOffcanvas from '../components/Dues/PaymentOffcanvas';
import BulkPaymentOffcanvas from '../components/Dues/BulkPaymentOffcanvas';
import Notification from '../components/common/Notification';
import { getDuesSummary, getPlatformDuesSummary, Due } from '../services/dues';
import PaymentLoader from '../components/common/PaymentLoader';
import { Users, Building } from 'lucide-react';

const Dues: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'staff' | 'platform'>('staff');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [selectedCard, setSelectedCard] = useState<string | null>(null);
  const [summary, setSummary] = useState({
    totalDues: 0,
    paidDues: 0,
    pendingDues: 0,
    overdueDues: 0,
    totalAmountDue: 0,
  });
  const [platformSummary, setPlatformSummary] = useState({
    totalDues: 0,
    paidDues: 0,
    pendingDues: 0,
    overdueDues: 0,
    totalAmountDue: 0,
    totalAmountPaid: 0,
    totalTeams: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPaymentOffcanvasOpen, setIsPaymentOffcanvasOpen] = useState(false);
  const [isBulkPaymentOffcanvasOpen, setIsBulkPaymentOffcanvasOpen] = useState(false);
  const [selectedDue, setSelectedDue] = useState<Due | null>(null);
  const [selectedDuesForBulk, setSelectedDuesForBulk] = useState<Due[]>([]);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info';
    title: string;
    message?: string;
    isVisible: boolean;
  }>({
    type: 'info',
    title: '',
    isVisible: false,
  });

  useEffect(() => {
    fetchSummary();
  }, []);

  const fetchSummary = async () => {
    try {
      setLoading(true);
      if (activeTab === 'staff') {
        const data = await getDuesSummary();
        setSummary(data);
      } else {
        const data = await getPlatformDuesSummary();
        setPlatformSummary(data);
      }
      setError(null);
    } catch (err) {
      setError('Failed to fetch dues summary');
    } finally {
      setLoading(false);
    }
  };

  // Fetch summary when tab changes
  useEffect(() => {
    fetchSummary();
  }, [activeTab]);

  const handleExport = () => {
    alert('Export functionality will be implemented here.');
  };

  const handleCardClick = (cardName: string, status?: string) => {
    setSelectedCard(cardName);
    setStatusFilter(status || 'All');
  };

  const handlePayNow = (due: Due) => {
    setSelectedDue(due);
    setIsPaymentOffcanvasOpen(true);
  };

  const handleBulkPay = (dues: Due[]) => {
    setSelectedDuesForBulk(dues);
    setIsBulkPaymentOffcanvasOpen(true);
  };

  const handlePaymentSuccess = () => {
    // Refresh the summary data after successful payment
    fetchSummary();
    // Show success notification
    setNotification({
      type: 'success',
      title: 'Payment Successful!',
      message: 'The due status has been updated successfully.',
      isVisible: true,
    });
  };

  const handleClosePaymentOffcanvas = () => {
    setIsPaymentOffcanvasOpen(false);
    setSelectedDue(null);
  };

  const handleCloseBulkPaymentOffcanvas = () => {
    setIsBulkPaymentOffcanvasOpen(false);
    setSelectedDuesForBulk([]);
  };

  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, isVisible: false }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center [min-height:765px]">
        <PaymentLoader
          type="wallet"
          message="Loading your dues and payments..."
          size="large"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">My Dues</h2>

          {/* Tab Navigation */}
          <div className="flex space-x-8 mb-6 bg-gray-100 p-4 rounded-lg">
            <button
              onClick={() => setActiveTab('staff')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === 'staff'
                  ? ' transition-colors group   bg-orange-50 text-orange-600 border-r-2 border-b-2  border-orange-600 '
                  : 'transition-colors group   bg-white text-blue-600 border-r-2 border-blue-600 hover:bg-orange-50 hover:border-b-2  hover:text-orange-600 hover:border-orange-600 '
             
              }`}
            >
              <Users size={20} />
              <span>Staff Dues</span>
            </button>
            <button
              onClick={() => setActiveTab('platform')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === 'platform'
                  ? ' transition-colors group   bg-orange-50 text-orange-600 border-r-2 border-b-2  border-orange-600 '
                  : 'transition-colors group   bg-white text-blue-600 border-r-2 border-blue-600 hover:bg-orange-50 hover:border-b-2  hover:text-orange-600 hover:border-orange-600 '
             
                }`}
            >
              <Building size={20} />
              <span>Platform Dues</span>
            </button>
          </div>

          {/* Summary Cards */}
          {activeTab === 'staff' ? (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <SummaryCard
                title="Total Dues"
                value={summary.totalDues}
                // className="bg-gray-100"
                onClick={() => handleCardClick('Total Dues', 'All')}
                selected={selectedCard === 'Total Dues'}
              />
              <SummaryCard
                title="Paid Dues"
                value={summary.paidDues}
                // className="bg-green-100"
                onClick={() => handleCardClick('Paid Dues', 'Paid')}
                selected={selectedCard === 'Paid Dues'}
              />
              <SummaryCard
                title="Pending Dues"
                value={summary.pendingDues}
                // className="bg-yellow-100"
                onClick={() => handleCardClick('Pending Dues', 'Pending')}
                selected={selectedCard === 'Pending Dues'}
              />
              <SummaryCard
                title="Overdue Dues"
                value={summary.overdueDues}
                // className="bg-red-100"
                onClick={() => handleCardClick('Overdue Dues', 'Overdue')}
                selected={selectedCard === 'Overdue Dues'}
              />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <SummaryCard
                title="Total Leagues"
                value={platformSummary.totalDues}
                // className="bg-gray-100"
                onClick={() => handleCardClick('Total Leagues', 'All')}
                selected={selectedCard === 'Total Leagues'}
              />
              <SummaryCard
                title="Paid Leagues"
                value={platformSummary.paidDues}
                // className="bg-green-100"
                onClick={() => handleCardClick('Paid Leagues', 'Paid')}
                selected={selectedCard === 'Paid Leagues'}
              />
              <SummaryCard
                title="Pending Leagues"
                value={platformSummary.pendingDues}
                // className="bg-yellow-100"
                onClick={() => handleCardClick('Pending Leagues', 'Pending')}
                selected={selectedCard === 'Pending Leagues'}
              />
              <SummaryCard
                title="Total Teams"
                value={platformSummary.totalTeams}
                // className="bg-blue-100"
                onClick={() => handleCardClick('Total Teams', 'All')}
                selected={selectedCard === 'Total Teams'}
              />
            </div>
          )}

          {/* Search and Filter */}
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 md:space-x-4 mb-6">
            <SearchBar className="w-full md:w-1/2" onSearch={setSearchTerm} />
            <FilterAndExportControls
              className="w-full md:w-1/2 lg:w-1/4"
              onFilterChange={(status) => {
                setStatusFilter(status);
                setSelectedCard(null); // Deselect card when filter is changed manually
              }}
              onExport={handleExport}
              currentFilter={statusFilter}
            />
          </div>

          {/* Dues Table */}
          <div className="border rounded-lg overflow-hidden mb-6">
            {activeTab === 'staff' ? (
              <DuesTable
                search={searchTerm}
                statusFilter={statusFilter}
                onDataChange={fetchSummary}
                onPayNow={handlePayNow}
                onBulkPay={handleBulkPay}
              />
            ) : (
              <PlatformDuesTable
                search={searchTerm}
                statusFilter={statusFilter}
                onDataChange={fetchSummary}
              />
            )}
          </div>

          {/* Total Amount Due */}
          <div className="flex justify-end mt-4">
            {activeTab === 'staff' ? (
              <p className="text-lg font-bold text-gray-900">
                Total Amount Due: <span className="text-red-500">${summary.totalAmountDue.toLocaleString()}</span>
              </p>
            ) : (
              <div className="text-right space-y-1">
                <p className="text-lg font-bold text-gray-900">
                  Total Amount Pending: <span className="text-red-500">${platformSummary.totalAmountDue.toLocaleString()}</span>
                </p>
                <p className="text-sm text-gray-600">
                  Total Amount Paid: <span className="text-green-600">${platformSummary.totalAmountPaid.toLocaleString()}</span>
                </p>
              </div>
            )}
          </div>
        </div>



        {/* Bulk Payment Offcanvas */}
        {/* <BulkPaymentOffcanvas
          isOpen={isBulkPaymentOffcanvasOpen}
          onClose={handleCloseBulkPaymentOffcanvas}
          selectedDues={selectedDuesForBulk}
          onPaymentSuccess={handlePaymentSuccess}
        /> */}
 
        {/* Notification */}
        <Notification
          type={notification.type}
          title={notification.title}
          message={notification.message}
          isVisible={notification.isVisible}
          onClose={handleCloseNotification}
        />
      </div>


      <div>
        {/* Payment Offcanvas */}
        <PaymentOffcanvas
          isOpen={isPaymentOffcanvasOpen}
          onClose={handleClosePaymentOffcanvas}
          due={selectedDue}
          onPaymentSuccess={handlePaymentSuccess}
        />
      </div>



    </>


  );
};

export default Dues;