import React, { useState, useEffect } from 'react';
import SummaryCard from '../components/Staff/SummaryCard';
import SearchBar from '../components/Staff/SearchBar';
import FilterAndAddMemberControls from '../components/Staff/FilterAndAddMemberControls';
import StaffTable from '../components/Staff/StaffTable';
import StaffBankAccounts from '../components/Staff/StaffBankAccounts';
import ImportUsersModal from '../components/Staff/ImportUsersModal';
import { getStaffSummary, StaffMember } from '../services/staff';
import PaymentLoader from '../components/common/PaymentLoader';
import DeleteConfirmModal from '../components/Staff/DeleteConfirmModal';
import AddBankAccountModal from '../components/Staff/AddBankAccountModal';
import StaffBankTransferModal from '../components/Staff/StaffBankTransferModal';

const Staff: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'staff' | 'bank-accounts'>('staff');
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('All');
  const [selectedCard, setSelectedCard] = useState<string | null>(null);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedStaffId, setSelectedStaffId] = useState<string | null>(null);
  const [summary, setSummary] = useState({
    totalStaff: 0,
    Staff: 0,
    referee: 0,
    totalAmountDue: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSummary();
  }, []);

  const fetchSummary = async () => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 2000));
      const data = await getStaffSummary();
      setSummary(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch staff summary');
    } finally {
      setLoading(false);
    }
  };

  const handleImportUsers = () => {
    setIsImportModalOpen(true);
  };

  const handleImportSuccess = () => {
    fetchSummary();
  };

  const handleCardClick = (cardName: string, role?: string) => {
    setSelectedCard(cardName);
    setRoleFilter(role || 'All');
  };

  const handleDeleteStaffClick = (id: string) => {
    setSelectedStaffId(id);
    setShowDeleteModal(true);
  };

  const confirmDeleteStaff = () => {
    if (!selectedStaffId) return;

    alert(`Staff member with ID ${selectedStaffId} would be deleted (functionality not implemented in this demo).`);

    setSelectedStaffId(null);
    setShowDeleteModal(false);
  };

  return (
    <div className="space-y-6">
      {/* Header with Tabs */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Staff Management</h1>
            <p className="text-gray-600">Manage your staff members and their bank accounts</p>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('staff')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'staff'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Staff Members
            </button>
            <button
              onClick={() => setActiveTab('bank-accounts')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'bank-accounts'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Bank Accounts
            </button>
          </nav>
        </div>
      </div>

      {activeTab === 'staff' && (
        <>
          {/* Summary Cards */}
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <PaymentLoader
                type="setup"
                message="Loading staff summary..."
                size="large"
                showQuotes={true}
              />
            </div>
          ) : error ? (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <SummaryCard
                title="Total Staff"
                value={summary.totalStaff}
                icon="users"
                color="blue"
                isSelected={selectedCard === 'Total Staff'}
                onClick={() => handleCardClick('Total Staff')}
              />
              <SummaryCard
                title="Staff"
                value={summary.Staff}
                icon="user"
                color="green"
                isSelected={selectedCard === 'Staff'}
                onClick={() => handleCardClick('Staff', 'Staff')}
              />
              <SummaryCard
                title="Referee"
                value={summary.referee}
                icon="shield"
                color="purple"
                isSelected={selectedCard === 'Referee'}
                onClick={() => handleCardClick('Referee', 'Referee')}
              />
              <SummaryCard
                title="Total Amount Due"
                value={summary.totalAmountDue}
                icon="dollar"
                color="orange"
                isSelected={selectedCard === 'Total Amount Due'}
                onClick={() => handleCardClick('Total Amount Due')}
              />
            </div>
          )}

          {/* Search and Controls */}
          <div className="bg-white rounded-xl shadow-sm border p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <SearchBar
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
                placeholder="Search staff members..."
              />
              <FilterAndAddMemberControls
                roleFilter={roleFilter}
                onRoleFilterChange={setRoleFilter}
                onImportUsers={handleImportUsers}
              />
            </div>
          </div>

          {/* Staff Table */}
          <div className='border rounded-lg overflow-hidden'>
            <StaffTable
              search={searchTerm}
              roleFilter={roleFilter}
              onDelete={handleDeleteStaffClick}
            />
          </div>
        </>
      )}

      {activeTab === 'bank-accounts' && <StaffBankAccounts />}

      {/* Modals */}
      <div>
        <ImportUsersModal
          isOpen={isImportModalOpen}
          onClose={() => setIsImportModalOpen(false)}
          onImportSuccess={handleImportSuccess}
        />

        <DeleteConfirmModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={confirmDeleteStaff}
          staffName={selectedStaffId || ''}
        />
      </div>
    </div>
  );
};

export default Staff;
