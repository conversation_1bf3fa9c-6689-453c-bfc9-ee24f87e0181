import React, { useState, useEffect } from 'react';
import {
  Wallet as WalletIcon,
  Plus,
  Minus,
  CreditCard,
  ArrowUpRight,
  ArrowDownLeft,
  RefreshCw,
  Eye,
  EyeOff,
  Settings,
  History
} from 'lucide-react';
import { getWallet, addMoneyToWallet, withdrawFromWallet, getWalletTransactions, Wallet as WalletType } from '../services/wallet';
import AddMoneyModal from '../components/Wallet/AddMoneyModal';
import WithdrawModal from '../components/Wallet/WithdrawModal';
import TransactionHistory from '../components/Wallet/TransactionHistory';
import WalletSettings from '../components/Wallet/WalletSettings';

import PaymentLoader from '../components/common/PaymentLoader';

const Wallet: React.FC = () => {
  const [wallet, setWallet] = useState<WalletType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showBalance, setShowBalance] = useState(true);
  const [showAddMoneyModal, setShowAddMoneyModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [showTransactionHistory, setShowTransactionHistory] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);

  const fetchWallet = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const walletData = await getWallet();
      setWallet(walletData);
    } catch (err) {
      console.error('Error fetching wallet:', err);
      setError('Failed to load wallet information. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRecentTransactions = async () => {
    try {
      const transactions = await getWalletTransactions(3, 0); // Get last 3 transactions
      setRecentTransactions(transactions);
    } catch (err) {
      console.error('Error fetching recent transactions:', err);
      setRecentTransactions([]);
    }
  };

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await fetchWallet();
      await fetchRecentTransactions();
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchWallet();
    fetchRecentTransactions();
  }, []);

  const handleAddMoney = async (amount: number, pin: string, bankAccountId?: string) => {
    try {
      const result = await addMoneyToWallet(amount, pin, bankAccountId);
      if (result.success && wallet) {
        setWallet({ ...wallet, balance: typeof result.newBalance === 'number' ? result.newBalance : 0 });
        setShowAddMoneyModal(false);
        fetchRecentTransactions(); // Refresh recent transactions
        return { success: true };
      } else {
        return { success: false, message: result.message || 'Failed to add money' };
      }
    } catch (error) {
      return { success: false, message: 'Failed to add money. Please try again.' };
    }
  };

  const handleWithdraw = async (amount: number, pin: string, bankAccountId?: string, paymentMethod?: string) => {
    try {
      const result = await withdrawFromWallet(amount, pin, bankAccountId, paymentMethod);
      if (result.success && wallet) {
        setWallet({ ...wallet, balance: typeof result.newBalance === 'number' ? result.newBalance : 0 });
        setShowWithdrawModal(false);
        fetchRecentTransactions(); // Refresh recent transactions
        return { success: true };
      } else {
        return { success: false, message: result.message || 'Failed to withdraw money' };
      }
    } catch (error) {
      return { success: false, message: 'Failed to withdraw money. Please try again.' };
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-xl flex items-center justify-center  [min-height:765px] p-8">
          <PaymentLoader
            type="wallet"
            message="Loading your wallet..."
            size="large"
            showQuotes={true}
          />
        </div>
      </div>
    );
  }

  if (error || !wallet) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <p className="text-red-600 mb-4">{error || 'Failed to load wallet'}</p>
          <button
            onClick={fetchWallet}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="space-y-6">
        {/* Wallet Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg text-white p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">

              <div>

                <div className="flex items-start space-x-3">
                  <h2 className="text-2xl font-bold">My Wallet</h2> <WalletIcon size={32} />
                  </div>
                <p className="text-blue-100">Primary Bank: {wallet.primaryBank}</p>
              </div>

            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors disabled:opacity-50"
              >
                <RefreshCw size={20} className={isRefreshing ? 'animate-spin' : ''} />
              </button>
              <button
                onClick={() => setShowSettings(true)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <Settings size={20} />
              </button>
            </div>
          </div>

          {/* Balance Display */}
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm mb-1">Available Balance</p>
              <div className="flex items-center space-x-3">
                <p className="text-3xl font-bold">
                  {showBalance ? `$${(typeof wallet.balance === 'number' ? wallet.balance : 0).toFixed(2)}` : '••••••'}
                </p>
                <button
                  onClick={() => setShowBalance(!showBalance)}
                  className="p-1 hover:bg-white/10 rounded transition-colors"
                >
                  {showBalance ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
            </div>
            <div className="text-right">
              <p className="text-blue-100 text-sm">Wallet ID</p>
              <p className="text-sm font-mono">{wallet.walletId}</p>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => setShowAddMoneyModal(true)}
            className="bg-white rounded-xl shadow-sm border p-6 hover:shadow-md transition-shadow group"
          >
            <div className="flex items-center justify-between space-x-4">
              
              <div className="text-left">
                <h3 className="font-semibold text-gray-900">Add Money</h3>
                <p className="text-sm text-gray-500">Top up your wallet</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors">
                <Plus size={24} className="text-green-600" />
              </div>
            </div>
          </button>

          <button
            onClick={() => setShowWithdrawModal(true)}
            className="bg-white rounded-xl shadow-sm border p-6 hover:shadow-md transition-shadow group"
          >
            <div className="flex items-center justify-between space-x-4">
              
              <div className="text-left">
                <h3 className="font-semibold text-gray-900">Withdraw</h3>
                <p className="text-sm text-gray-500">Transfer to bank</p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center group-hover:bg-red-200 transition-colors">
                <Minus size={24} className="text-red-600" />
              </div>
            </div>
          </button>

          <button
            onClick={() => setShowTransactionHistory(true)}
            className="bg-white rounded-xl shadow-sm border p-6 hover:shadow-md transition-shadow group"
          >
            <div className="flex items-center justify-between space-x-4">
              
              <div className="text-left">
                <h3 className="font-semibold text-gray-900">History</h3>
                <p className="text-sm text-gray-500">View transactions</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                <History size={24} className="text-blue-600" />
              </div>
            </div>
          </button>


        </div>

        {/* Recent Transactions Preview */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 ">Recent Transactions</h3>
            <div className="flex items-center space-x-4" onClick={() => setShowTransactionHistory(true)}>
              <p className="text-sm text-gray-500 cursor-pointer">View all</p>
            </div>
          </div>


          {/* Real recent transactions */}
          <div className="space-y-4">
            {recentTransactions.length === 0 ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <CreditCard size={24} className="text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No recent transactions</h3>
                <p className="text-gray-500">Start using your wallet to see transactions here.</p>
              </div>
            ) : (
              recentTransactions.map((transaction: any) => {
                const isCredit = transaction.type === 'credit';
                const IconComponent = isCredit ? ArrowDownLeft : ArrowUpRight;
                const iconBgColor = isCredit ? 'bg-green-100' : 'bg-red-100';
                const iconColor = isCredit ? 'text-green-600' : 'text-red-600';
                const amountColor = isCredit ? 'text-green-600' : 'text-red-600';
                const amountPrefix = isCredit ? '+' : '-';

                return (
                  <div key={transaction.id} className="flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg transition-colors">
                    <div className="flex items-center space-x-3">
                      <div className={`w-10 h-10 ${iconBgColor} rounded-full flex items-center justify-center`}>
                        <IconComponent size={16} className={iconColor} />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{transaction.description}</p>
                        <p className="text-sm text-gray-500">{transaction.provider || 'Wallet Transaction'}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-semibold ${amountColor}`}>
                        {amountPrefix}${Math.abs(transaction.amount).toFixed(2)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date(transaction.date).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>

      </div>


      <div>
        {/* Modals */}
        {showAddMoneyModal && (
          <AddMoneyModal
            isOpen={showAddMoneyModal}
            onClose={() => setShowAddMoneyModal(false)}
            onAddMoney={handleAddMoney}
            currentBalance={typeof wallet.balance === 'number' ? wallet.balance : 0}
            primaryBank={wallet.primaryBank}
          />
        )}

        {showWithdrawModal && (
          <WithdrawModal
            isOpen={showWithdrawModal}
            onClose={() => setShowWithdrawModal(false)}
            onWithdraw={handleWithdraw}
            currentBalance={typeof wallet.balance === 'number' ? wallet.balance : 0}
            primaryBank={wallet.primaryBank}
          />
        )}

        {showTransactionHistory && (
          <TransactionHistory
            isOpen={showTransactionHistory}
            onClose={() => setShowTransactionHistory(false)}
          />
        )}

        {showSettings && (
          <WalletSettings
            isOpen={showSettings}
            onClose={() => setShowSettings(false)}
            wallet={wallet}
            onWalletUpdate={setWallet}
          />
        )}
      </div>
    </div>
  );
};

export default Wallet;
