import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { getStaffDetails, getStaffTransactions, type StaffDetail } from '../services/staffDetails';
import { getStaffBankAccountsById, type StaffBankAccount } from '../services/staff';
import ProfileCard from '../components/StaffDetails/ProfileCard';
import PaymentSummaryCard from '../components/StaffDetails/PaymentSummaryCard';
import Tabs from '../components/StaffDetails/Tabs';
import StaffTransactionsTable from '../components/StaffDetails/StaffTransactionsTable';
import SearchBar from '../components/Transactions/SearchBar'; // Reusing existing search bar
import FilterAndExportControls from '../components/Transactions/FilterAndExportControls'; // Reusing filter
import Breadcrumb from '../components/common/Breadcrumb';
import PaymentLoader from '../components/common/PaymentLoader';
import AddBankAccountModal from '../components/Staff/AddBankAccountModal';
import { Plus, Building2, Star, CreditCard, User, Hash, Calendar } from 'lucide-react';

const StaffDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [staff, setStaff] = useState<StaffDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'gameActivity' | 'transactions' | 'bankAccounts'>('transactions');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [transactionCount, setTransactionCount] = useState(0);
  const [staffBankAccounts, setStaffBankAccounts] = useState<StaffBankAccount[]>([]);
  const [showAddBankModal, setShowAddBankModal] = useState(false);

  useEffect(() => {
    console.log('StaffDetail: id from useParams:', id);
    if (id && id !== 'undefined') {
      fetchStaffDetails(id);
      fetchTransactionCount(id);
    } else {
      console.error('StaffDetail: Invalid or missing id:', id);
      setError('Invalid staff ID');
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    if (activeTab === 'bankAccounts' && id) {
      const fetchBankAccounts = async () => {
        try {
          console.log('Fetching bank accounts on tab change for staff ID:', id);
          const bankAccounts = await getStaffBankAccountsById(id);
          console.log('Received bank accounts on tab change:', bankAccounts);
          setStaffBankAccounts(bankAccounts);
        } catch (error) {
          console.error('Error fetching bank accounts on tab change:', error);
        }
      };
      
      fetchBankAccounts();
    }
  }, [activeTab, id]);

  const fetchStaffDetails = async (staffId: string) => {
    try {
      setLoading(true);
      const data = await getStaffDetails(staffId);
      setStaff(data);
      setError(null);
      
      // Also fetch bank accounts
      try {
        console.log('Fetching bank accounts for staff ID:', staffId);
        const bankAccounts = await getStaffBankAccountsById(staffId);
        console.log('Received bank accounts:', bankAccounts);
        setStaffBankAccounts(bankAccounts);
      } catch (bankError) {
        console.error('Error fetching bank accounts:', bankError);
      }
    } catch (err) {
      setError('Failed to fetch staff details');
    } finally {
      setLoading(false);
    }
  };

  const fetchTransactionCount = async (staffId: string) => {
    try {
      // Fetch just the first page to get the total count
      const response = await getStaffTransactions(staffId, { limit: 1, offset: 0 });
      setTransactionCount(response.pagination.total);
    } catch (err) {
      console.error('Failed to fetch transaction count:', err);
      setTransactionCount(0);
    }
  };

  const handleExport = () => {
    alert('Export functionality will be implemented here.');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center [min-height:765px] ">
        <PaymentLoader
          type="setup"
          message="Loading staff management dashboard..."
          size="large"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!staff) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-gray-600">Staff member not found.</div>
      </div>
    );
  }

  const breadcrumbItems = [
    { label: 'Dashboard', path: '/dashboard' },
    { label: 'Staff', path: '/staff' },
    { label: staff.full_name },
  ];

  // Remove the local filtering since it's now handled by the API
  // const filteredTransactions = staff.transactions.filter(transaction => {
  //   const matchesSearch = searchTerm === '' ||
  //     transaction.transactionId.toLowerCase().includes(searchTerm.toLowerCase()) ||
  //     transaction.paidByReceivedFrom.toLowerCase().includes(searchTerm.toLowerCase()) ||
  //     transaction.type.toLowerCase().includes(searchTerm.toLowerCase());
  //
  //   const matchesStatus = statusFilter === 'All' || transaction.status === statusFilter;

  //   return matchesSearch && matchesStatus;
  // });

  console.log(staff.upcomingPayment, 'staff')

  return (
    <div className="space-y-6">
      <Breadcrumb items={breadcrumbItems} />

      {/* Profile and Payment Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ProfileCard staff={staff} />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <PaymentSummaryCard
            title="Total Payment Paid"
            value={staff?.totalPaymentPaid}
            dateLabel="Last Paid"
            date={staff.lastPaidDate}
          />
        </div>

      </div>

      {/* Tabs and Controls */}
      <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 md:space-x-4">
        <Tabs
          activeTab={activeTab}
          gameActivityCount={staff.gameActivityCount}
          transactionsCount={transactionCount}
          bankAccountsCount={staffBankAccounts.length}
          onTabChange={setActiveTab}
        />
        <div className="flex items-center space-x-4 w-full md:w-auto">
          <SearchBar className="flex-grow" onSearch={setSearchTerm} />
          <FilterAndExportControls
            className="w-auto"
            onFilterChange={setStatusFilter}
            onExport={handleExport}
            currentFilter={statusFilter}
          />
        </div>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'gameActivity' && (
        <div className="bg-white rounded-xl shadow-sm border p-6 text-center py-12">
          <p className="text-gray-500">Game Activity details will be displayed here.</p>
        </div>
      )}

      {activeTab === 'transactions' && (
        <StaffTransactionsTable
          staffId={id!}
          searchTerm={searchTerm}
          statusFilter={statusFilter}
          className="bg-white rounded-xl shadow-sm border"
        />
      )}

      {activeTab === 'bankAccounts' && (
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Bank Accounts</h3>
            <button
              onClick={() => setShowAddBankModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Plus size={16} />
              <span>Add Bank Account</span>
            </button>
          </div>

          {!staffBankAccounts || staffBankAccounts.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Building2 className="text-gray-400 w-8 h-8" />
              </div>
              <h4 className="text-lg font-medium text-gray-900 mb-2">No Bank Accounts</h4>
              <p className="text-gray-500 mb-4">Add a bank account to enable payments to this staff member.</p>
              <button
                onClick={() => setShowAddBankModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Add First Bank Account
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {staffBankAccounts.map((account, index) => (
                <div
                  key={account.id}
                  className={`border rounded-lg p-4 ${
                    index === 0 ? 'border-blue-300 bg-blue-50' : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                        index === 0 ? 'bg-blue-200' : 'bg-blue-100'
                      }`}>
                        <User className="text-blue-600 w-6 h-6" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-semibold text-gray-900">{account.staffName}</h4>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                            Active
                          </span>
                          {index === 0 && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                              <Star size={12} className="mr-1" /> Primary
                            </span>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                          <div className="flex items-center space-x-2">
                            <Building2 size={14} />
                            <span>{account.bankName}</span>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <CreditCard size={14} />
                            <span className="capitalize">{account.accountType}</span>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Calendar size={14} />
                            <span>Added {new Date(account.createdAt).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric'
                            })}</span>
                          </div>
                        </div>
                        
                        <div className="mt-2">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">Account:</span>
                            <span className="text-sm font-mono">{account.accountNumber}</span>
                          </div>
                          
                          <div className="text-sm text-gray-500 mt-1">
                            Account Holder: {account.accountHolderName}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default StaffDetail; 
