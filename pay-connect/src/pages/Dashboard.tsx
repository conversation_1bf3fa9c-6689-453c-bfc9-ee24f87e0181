import React, { useState, useEffect } from 'react';

import BankCard from '../components/Dashboard/BankCard';
import SummaryCard from '../components/Dashboard/SummaryCard';
import ActionButton from '../components/Dashboard/ActionButton';
import EmptyState from '../components/common/EmptyState';
import AddMoneyModal from '../components/Wallet/AddMoneyModal';
import WithdrawModal from '../components/Wallet/WithdrawModal';
import WithdrawalProgress from '../components/Wallet/WithdrawalProgress';
import TransactionHistory from '../components/Wallet/TransactionHistory';

import { getDashboardData, DashboardData } from '../services/dashboard';
import { addMoneyToWallet, withdrawFromWallet } from '../services/wallet';
import PaymentLoader from '../components/common/PaymentLoader';
import { Awaiting, Mywallet, paymentAdd, paymentWidarw, upComingPayment } from '../components/importImages/images';

const Dashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddMoneyModal, setShowAddMoneyModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [showWithdrawalProgress, setShowWithdrawalProgress] = useState(false);
  const [currentWithdrawalId, setCurrentWithdrawalId] = useState<number | null>(null);
  const [showTransactionHistory, setShowTransactionHistory] = useState(false);


  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      // Add artificial delay to show the engaging loader
      await new Promise(resolve => setTimeout(resolve, 2000));
      const data = await getDashboardData();
      setDashboardData(data);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const handleAddMoney = async (amount: number, pin: string, bankAccountId?: string) => {
    try {
      const result = await addMoneyToWallet(amount, pin, bankAccountId);
      if (result.success) {
        // Refresh dashboard data to show updated balance
        await fetchDashboardData();
        setShowAddMoneyModal(false);
        return { success: true };
      } else {
        return { success: false, message: result.message || 'Failed to add money' };
      }
    } catch (error) {
      console.error('Error adding money:', error);
      return { success: false, message: 'Failed to add money. Please try again.' };
    }
  };

  const handleWithdraw = async (amount: number, pin: string, bankAccountId?: string, paymentMethod?: string) => {
    try {
      const result = await withdrawFromWallet(amount, pin, bankAccountId, paymentMethod);
      if (result.success) {
        // Refresh dashboard data to show updated balance
        await fetchDashboardData();
        setShowWithdrawModal(false);

        // Show withdrawal progress modal if we have a transaction ID
        if (result.transactionId) {
          setCurrentWithdrawalId(result.transactionId);
          setShowWithdrawalProgress(true);
        }

        return { success: true };
      } else {
        return { success: false, message: result.message || 'Failed to withdraw money' };
      }
    } catch (error) {
      console.error('Error withdrawing money:', error);
      return { success: false, message: 'Failed to withdraw money. Please try again.' };
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12 [min-height:765px]" >
          <PaymentLoader
            type="wallet"
            message="Loading your financial dashboard..."
            size="large"
            showQuotes={true}
          />
        </div>
      </div>
    );
  }

  // Show error state
  if (error || !dashboardData) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <p className="text-red-600 mb-4">{error || 'Failed to load dashboard data'}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const { stats, awaitingPayments, dueLists, transactionHistory, bankAccount } = dashboardData;

  return (
    <>

      <div className="space-y-6">
        {/* Top Row - Cards and Actions */}
        <div className="grid grid-cols-1 xl:grid-cols-12 lg:grid-cols-6 gap-6">
          {/* Bank Account Card */}
          <div className="lg:col-span-3">
            <BankCard
              accountNumber={bankAccount.accountNumber}
              bankName={bankAccount.bankName}
              cardType={bankAccount.cardType}
            />
          </div>

          {/* Summary Cards */}
          <div className="lg:col-span-6 grid grid-cols-1 sm:grid-cols-3 gap-4">
            <SummaryCard
              title="Total Wallet Balance"
              value={`$${(typeof stats.totalWalletBalance === 'number' ? stats.totalWalletBalance : 0).toFixed(2)}`}

              imageSrc={Mywallet}
              // bgColor="bg-orange-100"
              iconColor="text-orange-600"
            />
            <SummaryCard
              title="Total Amount Due"
              value={`$${stats.totalAmountDue}`}
              imageSrc={upComingPayment}
              // bgColor="bg-red-100"        
              iconColor="text-red-600"
            />
            <SummaryCard
              title="Total Awaiting Amount"
              value={`$${stats.totalAwaitingAmount}`}
              imageSrc={Awaiting}
              // bgColor="bg-blue-100"
              iconColor="text-blue-600"
            />
          </div>

          {/* Add/Withdraw Money Action Buttons */}
          <div className="lg:col-span-3">
            <div className="w-full max-w-[382px] aspect-[382/245] mx-auto">
              <div className="bg-white rounded-[30px] shadow-sm border p-3 border-dashed border-gray-300  flex flex-col">
                <div className="rounded-[30px] p-8 shadow-sm bg-[#27AE601A] flex-1 flex flex-col justify-center">
                  <div className="space-y-3 flex flex-col justify-center">
                    <ActionButton
                      label="Add Money"
                      imageSrc={paymentAdd}
                      onClick={() => setShowAddMoneyModal(true)}
                    />
                    <ActionButton
                      label="Withdraw"
                      imageSrc={paymentWidarw}
                      onClick={() => setShowWithdrawModal(true)}
                      variant="secondary"
                    />
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>

        {/* Bottom Row - Transaction Sections */}
        <div className='pt-6' >
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Awaiting Payment */}
            <div className="bg-white rounded-xl shadow-sm border min-h-100">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Awaiting Payment</h3>
                    <p className="text-xs text-gray-500">Feature in development</p>
                  </div>
                  <button
                    onClick={() => alert('Feature coming soon!')}
                    className="bg-blue-500 text-white px-3 py-1 rounded-md text-xs font-medium hover:bg-blue-600 transition-colors"
                  >
                    Coming Soon
                  </button>
                </div>
                <div className="space-y-4">

                  <div className="space-y-3 max-h-[380px] overflow-y-auto pr-2 scrollbar-hide">
                    {awaitingPayments.length === 0 ? (
                      <EmptyState
                        type="payments"
                        title="No awaiting payments"
                        description="All payments are up to date. New payments will appear here when they are due."
                        className="py-8"
                      />
                    ) : (
                      awaitingPayments.map((payment, index) => (
                        <div key={index} className="flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg transition-colors border border-gray-100">
                          <div className="flex-1">
                            <p className="text-sm text-gray-500 mb-2">{payment.subtitle}</p>
                            <p className="text-base font-medium text-blue-600">{payment.title}</p>
                            <p className="text-sm text-gray-500 mt-1">{payment.date}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-semibold text-gray-900">{payment.amount}</p>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Due Lists */}
            <div className="bg-white rounded-xl shadow-sm border min-h-100">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Due Lists</h3>
                    <p className="text-xs text-gray-500">Feature in development</p>
                  </div>
                  <button
                    onClick={() => alert('Feature coming soon!')}
                    className="bg-blue-500 text-white px-3 py-1 rounded-md text-xs font-medium hover:bg-blue-600 transition-colors"
                  >
                    Coming Soon
                  </button>
                </div>
                <div className="space-y-3 max-h-[380px] overflow-y-auto pr-2 scrollbar-hide">
                  <div className="space-y-3 ">
                    {dueLists.length === 0 ? (
                      <EmptyState
                        type="dues"
                        title="No dues found"
                        description="All dues have been paid or no dues have been created yet."
                        className="py-8"
                      />
                    ) : (
                      dueLists.map((due, index) => (


                        <div key={index} className="flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg transition-colors border border-gray-100">
                          <div className="flex-1">
                            <p className="text-sm text-gray-500 mb-2">{due.subtitle}</p>
                            <p className="text-base font-medium text-blue-600">{due.title}</p>
                            <p className="text-sm text-gray-500 mt-1">{due.date}</p>
                          </div>
                          <div className="text-right flex items-center space-x-3">
                            <p className="text-lg font-semibold text-gray-900">{due.amount}</p>
                            <button className="bg-green-500 text-white px-3 py-2 rounded-lg text-sm font-medium hover:bg-green-600 transition-colors">
                              Pay
                            </button>
                          </div>
                        </div>

                      ))
                    )}

                  </div>
                </div>
              </div>
            </div>

            {/* Transaction History */}
            <div className="bg-white rounded-xl shadow-sm border min-h-100">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Recent Transactions</h3>
                    <p className="text-xs text-gray-500">Latest wallet activity</p>
                  </div>
                  <button
                    onClick={() => setShowTransactionHistory(true)}
                    className="bg-blue-500 text-white px-3 py-1 rounded-md text-xs font-medium hover:bg-blue-600 transition-colors"
                  >
                    View All
                  </button>
                </div>
                <div className="space-y-3 max-h-[380px] overflow-y-auto pr-2 scrollbar-hide">
                  {transactionHistory.length === 0 ? (
                    <EmptyState
                      type="transactions"
                      title="No transaction history"
                      description="Transaction history will appear here once payments are processed."
                      className="py-8"
                    />
                  ) : (
                    transactionHistory.map((history, index) => (
                      <div key={index}>
                        <p className="text-sm font-medium text-gray-600 mb-3">{history.dateHeader}</p>

                        {/* Scrollable container with hidden scrollbar */}
                        <div className="space-y-3 ">
                          {history.items.map((item, itemIndex) => (
                            <div
                              key={itemIndex}
                              className="flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg transition-colors border border-gray-100"
                            >
                              <div className="flex-1">
                                <p className="text-sm text-gray-500 mb-2">{item.subtitle}</p>
                                <p className="text-base font-medium text-blue-600">{item.title}</p>
                                <p className="text-sm text-gray-500 mt-1">{item.date}</p>
                              </div>
                              <div className="text-right">
                                <p
                                  className={`text-lg font-semibold ${
                                    item.type === 'pending' || item.status === 'pending'
                                      ? 'text-yellow-600'
                                      : item.type === 'received'
                                        ? 'text-green-600'
                                        : item.type === 'withdrawn'
                                          ? 'text-red-600'
                                          : 'text-blue-600'
                                    }`}
                                >
                                  {item.amount}
                                </p>
                                {(item.type === 'pending' || item.status === 'pending') && (
                                  <span className="text-xs text-yellow-600 font-medium">
                                    Pending
                                  </span>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                    ))
                  )}
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>

      {/* Modals */}
      {showAddMoneyModal && (
        <AddMoneyModal
          isOpen={showAddMoneyModal}
          onClose={() => setShowAddMoneyModal(false)}
          onAddMoney={handleAddMoney}
          currentBalance={typeof stats.totalWalletBalance === 'number' ? stats.totalWalletBalance : 0}
          primaryBank={bankAccount.bankName}
        />
      )}

      {showWithdrawModal && (
        <WithdrawModal
          isOpen={showWithdrawModal}
          onClose={() => setShowWithdrawModal(false)}
          onWithdraw={handleWithdraw}
          currentBalance={typeof stats.totalWalletBalance === 'number' ? stats.totalWalletBalance : 0}
          primaryBank={bankAccount.bankName}
        />
      )}

      {showWithdrawalProgress && currentWithdrawalId && (
        <WithdrawalProgress
          isOpen={showWithdrawalProgress}
          onClose={() => setShowWithdrawalProgress(false)}
          transactionId={currentWithdrawalId}
        />
      )}

      {showTransactionHistory && (
        <TransactionHistory
          isOpen={showTransactionHistory}
          onClose={() => setShowTransactionHistory(false)}
        />
      )}
    </>
  );
};

export default Dashboard;
