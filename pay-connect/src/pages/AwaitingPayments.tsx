import React, { useState, useEffect } from 'react';
import SummaryCard from '../components/AwaitingPayments/SummaryCard';
import SearchBar from '../components/AwaitingPayments/SearchBar';
import FilterAndExportControls from '../components/AwaitingPayments/FilterAndExportControls';
import PaymentsTable from '../components/AwaitingPayments/PaymentsTable';
import Offcanvas from '../components/common/Offcanvas';
import AwaitingPaymentDetailOffcanvas from '../components/AwaitingPayments/AwaitingPaymentDetailOffcanvas';
import { getPaymentSummary } from '../services/awaitingPayments';
import PaymentLoader from '../components/common/PaymentLoader';

const AwaitingPayments: React.FC = () => {
  const [selectedCard, setSelectedCard] = useState('totalIncoming');
  const [search, setSearch] = useState('');
  const [summary, setSummary] = useState({
    totalIncoming: 0,
    overdue: 0,
    pending: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOffcanvasOpen, setIsOffcanvasOpen] = useState(false);
  const [selectedPaymentId, setSelectedPaymentId] = useState<string | null>(null);

  // Add filter state
  const [selectedGame, setSelectedGame] = useState<string | null>(null);
  const [selectedSeason, setSelectedSeason] = useState<string | null>(null);
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);

  useEffect(() => {
    fetchSummary();
  }, []);

  const fetchSummary = async () => {
    try {
      setLoading(true);
      // Add artificial delay to show the engaging loader
      await new Promise(resolve => setTimeout(resolve, 2000));
      const data = await getPaymentSummary();
      setSummary(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch payment summary');
    } finally {
      setLoading(false);
    }
  };

  const handleCardClick = (card: string) => {
    setSelectedCard(card);
  };

  const handleViewDetails = (team_id: string) => {
    setSelectedPaymentId(team_id);
    setIsOffcanvasOpen(true);
  };

  const handleCloseOffcanvas = () => {
    setIsOffcanvasOpen(false);
    setSelectedPaymentId(null);
  };

  // Handlers for filter changes
  const handleGameChange = (value: string | null) => setSelectedGame(value);
  const handleSeasonChange = (value: string | null) => setSelectedSeason(value);
  const handleGroupChange = (value: string | null) => setSelectedGroup(value);


  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen  [min-height:765px]">
        <PaymentLoader
          type="wallet"
          message="Loading awaiting payments..."
          size="large"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className=" ">
      <h2 className="text-2xl font-bold text-gray-900">Awaiting Payments</h2>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 mb-6">
        <SummaryCard
          title="Total Incoming Dues"
          value={45}
          onClick={() => handleCardClick('totalIncoming')}
          selected={selectedCard === 'totalIncoming'}
        />
        <SummaryCard
          title="Over due Amount"
          value={78}
          onClick={() => handleCardClick('overdue')}
          selected={selectedCard === 'overdue'}
        />
        <SummaryCard
          title="Pending league Payment"
          value={90}
          onClick={() => handleCardClick('pending')}
          selected={selectedCard === 'pending'}
        />
      </div>

      {/* Search and Filter */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 items-center mb-6">
        <div className="row-span-3 ">
          <SearchBar className="w-full" onSearch={setSearch} />
        </div>

        <div className="row-span-2 col-span-2 ">
          <FilterAndExportControls
            className="w-full"
            selectedGame={selectedGame}
            selectedSeason={selectedSeason}
            selectedGroup={selectedGroup}
            onGameChange={handleGameChange}
            onSeasonChange={handleSeasonChange}
            onGroupChange={handleGroupChange}
          />
        </div>
      </div>

      {/* Payments Table */}
      <PaymentsTable
        className="bg-white rounded-xl shadow-sm border"
        search={search}
        onViewDetails={handleViewDetails}
        selectedGame={selectedGame}
        selectedSeason={selectedSeason}
        selectedGroup={selectedGroup}
      />
      {/* Offcanvas for details */}
      <Offcanvas
        isOpen={isOffcanvasOpen}
        onClose={handleCloseOffcanvas}
        title="Awaiting Payment Details"
        width="w-98"
      >
        <AwaitingPaymentDetailOffcanvas
          paymentId={selectedPaymentId}
          onClose={handleCloseOffcanvas}
        />
      </Offcanvas>
    </div>
  );
};

export default AwaitingPayments;
