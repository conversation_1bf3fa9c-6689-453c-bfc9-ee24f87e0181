import express, { Request, Response, RequestHandler } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import paymentRoutes from './routes/paymentRoutes';
import mobileApisRoutes from './routes/mobileApisRoutes';
import plaidRoutes from './routes/plaidRoutes';
import plaidTransferRoutes from './routes/plaidTransferRoutes';
import plaidStatusRoutes from './routes/plaidStatusRoutes';
import { errorHandler } from './middlewares/errorHandler';
import { authenticateTokenAndSession } from './middlewares/sessionAuth';
import dotenv from 'dotenv';
import userRoutes from './routes/userRoutes';
import securityEventRoutes from './routes/securityEventRoutes';
import walletRoutes from './routes/walletRoutes';
import staffRoutes from './routes/staffRoutes';
import paymentMethodRoutes from './routes/paymentMethodRoutes';
import duesRoutes from './routes/duesRoutes';
import { getConnection } from './config/db';

dotenv.config();

const app = express();

// Initialize database connection
getConnection();

// Security
app.use(helmet());
app.use(cors({ origin: 'http://localhost:5173', credentials: true }));
app.use(morgan('dev'));
app.use(express.json());

// Routes
app.use('/api/payments',  paymentRoutes);
app.use('/api/plaid', plaidRoutes);
app.use('/api/plaid-transfers', plaidTransferRoutes); // New Plaid transfer routes
app.use('/api/plaid-status', plaidStatusRoutes); // Enhanced Plaid status routes
app.use('/api/users', userRoutes);
app.use('/api/wallet', walletRoutes);
app.use('/api/staff', staffRoutes);
app.use('/api/security-events', securityEventRoutes);
app.use('/api/dues', duesRoutes); // Use dedicated dues routes
app.use('/api', paymentMethodRoutes);
app.use('/api/app', mobileApisRoutes);

// Health check
const healthCheckHandler: RequestHandler = (_req: Request, res: Response): void => {
  res.json({ status: 'ok' });
};

app.get('/api/health', healthCheckHandler);

// Error handler
app.use(errorHandler);

export default app;
