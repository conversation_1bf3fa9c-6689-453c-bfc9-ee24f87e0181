import { Request, Response } from 'express';
import { checkPinSetupRequired, getPrimaryBankAccount, getUserMasterWallet, setWalletMasterPin } from '../../services/walletService';
import logger from '../../utils/logger';
import { sendError, sendSuccess, sendUnauthorized, sendUserSuccess } from '../../utils/response';
import * as appBankServices from '../../services/appBankServices';
import { comparePin, hashPin } from '../../models/user';
import { generateAndSendOTP, invalidateUserOTPs, markOTPAsUsed, OTP_CONFIG, verifyOTPCode } from '../../services/otpService';
import { executeQuerySingle } from '../../utils/database';
import { sendWelcomeEmail } from '../../services/emailService';
import { createUserSession, enforceSingleSessionPolicy, getActiveSessionCount, hasUserEverHadSession } from '../../services/sessionService';



export const sendOtp = async (req: Request & { userId?: string; parent_user_id?: string }, res: Response) => {
  try {
    const userId = 121; // Get userId from authenticated token

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get user details
    const user = await executeQuerySingle(
      'SELECT * FROM tbl_users WHERE id = ?',
      [userId]
    );

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    // Invalidate all previous unused login OTPs
    await invalidateUserOTPs(user.id, 'login');

    // Generate, save and send new OTP
    const { otpCode, otpId, emailSent } = await generateAndSendOTP(user.id,'<EMAIL>', 'login');

    logger.info('OTP resent', {
      userId: user.id,
      email: '<EMAIL>',
      otpId,
      emailSent,
      expiresInMinutes: OTP_CONFIG.EXPIRY_MINUTES
    });

    sendUserSuccess(res, 'AUTH', 'OTP_RESEND', {
      message: 'New security code sent successfully',
      userId: user.id,
      email: user.email,
      emailSent,
      // Remove this in production - OTP should be sent via SMS/Email
      otp: process.env.NODE_ENV === 'development' ? otpCode : undefined,
      expiresInMinutes: OTP_CONFIG.EXPIRY_MINUTES
    });

  } catch (error) {
    logger.error('Resend OTP error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to resend OTP', 500, error instanceof Error ? error.message : 'Unknown error');
  }
};


export const verifyOTP = async (req: Request & { userId?: string; parent_user_id?: string }, res: Response) => {
  try {
    const { otpCode } = req.body;
    const userId = req.userId; // Get userId from authenticated token
    const parentUserId = req.parent_user_id; // Get parent_user_id from authenticated token


    // Validate input
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!otpCode) {
      return sendError(res, 'OTP code is required', 400);
    }

    if (typeof otpCode !== 'string' || otpCode.length !== OTP_CONFIG.LENGTH) {
      return sendError(res, `OTP must be ${OTP_CONFIG.LENGTH} digits`, 400);
    }

    // Get user by ID from token
    const user = await executeQuerySingle(
      'SELECT * FROM tbl_users WHERE id = ?',
      [userId]
    );

    if (!user) {
      logger.warn('User not found during OTP verification', { userId });
      return sendError(res, 'User not found', 404);
    }

    // Verify OTP using service
    const otpVerification = await verifyOTPCode(parseInt(userId), otpCode, 'login');

    if (!otpVerification.isValid) {
      logger.warn('OTP verification failed', {
        userId: user.id,
        email: user.email,
        otpCode,
        error: otpVerification.error
      });
      return sendError(res, otpVerification.error || 'Invalid OTP code', 400);
    }

    // Mark OTP as used
    if (otpVerification.otpRecord) {
      await markOTPAsUsed(otpVerification.otpRecord.id);
    }

    // Check if user has wallet PIN setup
    // const requiresPinSetup = await checkPinSetupRequired(user.id);

    // Check current active sessions before creating new one
    // const currentActiveSessions = await getActiveSessionCount(user.id);
    // logger.info('User active sessions before login', {
    //   userId: user.id,
    //   email: user.email,
    //   currentActiveSessions
    // });

    // Check if this is the user's first session (before creating new session)
    // const isFirstTimeUser = !(await hasUserEverHadSession(user.id));

    // Generate JWT token first
    // const token = jwt.sign(
    //   { id: user.id, team_connect_user_id: user.team_connect_user_id, master_parent_user_id: user.master_parent_user_id, email: user.email },
    //   process.env.JWT_SECRET || 'your-secret-key'
      
    // );

    // Create user session using the JWT token as session key (this will invalidate any existing sessions)
    // const userAgent = req.headers['user-agent'] || 'Unknown';
    // const ipAddress = req.ip || req.socket.remoteAddress || 'Unknown';

    // const { sessionId, expiresAt } = await createUserSession(
    //   user.id,
    //   token, // Use JWT token as session key
    //   userAgent,
    //   ipAddress
    // );

    // Enforce single session policy as an additional safety measure
    // await enforceSingleSessionPolicy(user.id);

    // Verify final session count
    // const finalActiveSessions = await getActiveSessionCount(user.id);
    // logger.info('User active sessions after login', {
    //   userId: user.id,
    //   email: user.email,
    //   // finalActiveSessions,
    //   // sessionId
    // });

    // Warn if somehow multiple sessions still exist
    // if (finalActiveSessions > 1) {
    //   logger.error('CRITICAL: Multiple active sessions detected after enforcement', {
    //     userId: user.id,
    //     email: user.email,
    //     finalActiveSessions,
    //     // sessionId
    //   });
    // }

    // Send welcome email for first-time users
    // if (isFirstTimeUser) {
      try {
        const userName = user.full_name || user.email.split('@')[0];
        const welcomeEmailSent = await sendWelcomeEmail('<EMAIL>', userName);

        logger.info('Welcome email sent to first-time user', {
          userId: user.id,
          email: user.email,
          userName,
          emailSent: welcomeEmailSent
        });
      } catch (error) {
        // Don't fail the login if welcome email fails
        logger.error('Failed to send welcome email to first-time user', {
          userId: user.id,
          email: user.email,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    // }

    logger.info('OTP verified successfully', {
      userId: user.id,
      email: user.email,
      // parentUserId,
      // sessionId,
      // requiresPinSetup,
      // isFirstTimeUser
    });

    const responseData = {
      // requiresPinSetup,
      // isFirstTimeUser,
      user: {
        id: user.id,
        email: user.email,
        fullName: user.full_name,
        organizerId: user.organizer_id
      },
      // token,
      // session: {
      //   sessionId,
      //   expiresAt
      // }
    };


      sendUserSuccess(res, 'AUTH', 'OTP_VERIFIED', responseData);
  

  } catch (error) {
    logger.error('OTP verification error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'OTP verification failed', 500, error instanceof Error ? error.message : 'Unknown error');
  }
};
// Add or update a bank account for the user
export async function addUpdateBankAccount(req: Request, res: Response): Promise<void> {
  try {
    const { id, bank_name, account_number, routing_number, account_type, account_holder_name, userId } = req.body;
    if (!userId) {
      res.status(401).json({ success: false, message: 'User not authenticated' });
      return;
    }
    if (!bank_name || !account_number || !routing_number || !account_type || !account_holder_name) {
      res.status(400).json({ success: false, message: 'Missing required bank account fields' });
      return;
    }

    const result = await appBankServices.addOrUpdateBankAccount({
      id,
      bank_name,
      account_number,
      routing_number,
      account_type,
      account_holder_name,
      userId
    });

    if (id) {
      if (result.updated) {
        sendSuccess(res, {}, 'Bank account updated successfully', 201);
        return;
      } else {
        sendError(res, 'Bank account not found', 400);
        return;
      }
    } else {
      sendSuccess(res, {}, 'Bank account added successfully', 201);
      return;
    }
  } catch (error) {
    logger.error('Error upserting bank account', { error });
    sendError(res, 'Failed to add/update bank account', 500);
    return;
  }
};

// List all bank accounts for the user
export async function listBankAccounts(req: Request & { userId?: string }, res: Response): Promise<void> {
  try {
    const { userId } = req.query;
    if (!userId) {
      res.status(401).json({ success: false, message: 'User not authenticated' });
      return;
    }
    const accounts = await appBankServices.getBankAccounts(userId as string);
    sendSuccess(res, accounts, 'Bank accounts retrieved successfully', 200);
    return;
  } catch (error) {
    logger.error('Error listing bank accounts', { error });
    sendError(res, 'Failed to list bank accounts', 500);
    return;
  }
};

export async function listTransactionsHistory(req: Request & { userId?: string }, res: Response): Promise<void> {
  try {
    const userId = req.userId || (req.query.userId as string);
    if (!userId) {
      sendError(res, 'User not authenticated', 401);
      return;
    }
    const accounts = await appBankServices.getTransactionsHistory(userId);
    sendSuccess(res, accounts, 'Bank transaction history retrieved successfully', 200);
    return;
  } catch (error) {
    logger.error('Error listing bank accounts', { error });
    sendError(res, 'Failed to list bank accounts', 500);
    return;
  }
}

export async function listWallet(req: Request & { userId?: string }, res: Response): Promise<void> {
  try {
    const userId = req.userId || (req.query.userId as string);
    if (!userId) {
      sendError(res, 'User not authenticated', 401);
      return;
    }
    const accounts = await appBankServices.getWallets(userId);
    sendSuccess(res, accounts, 'Wallet retrieved successfully', 200);
    return;
  } catch (error) {
    logger.error('Error listing bank accounts', { error });
    sendError(res, 'Failed to list bank accounts', 500);
    return;
  }
}


export async function setUpPin(req: Request & { userId?: string }, res: Response): Promise<void> {
  try {
    const userId = req.userId || (req.query.userId as string);
    if (!userId) {
      sendError(res, 'User not authenticated', 401);
      return;
    }
    const pin = req.body.pin;
   
     if (!pin || typeof pin !== 'string') {
      return sendError(res, 'Master PIN is required', 400);
    }

    // Validate PIN format (should be 4-6 digits)
    if (!/^\d{4,6}$/.test(pin)) {
      return sendError(res, 'PIN must be 4-6 digits', 400);
    }
   const hashedPin = await hashPin(pin);
    // Assuming setPin is a function that sets the user's PIN
    await appBankServices.setPin(userId, hashedPin);
    
    sendSuccess(res, {}, 'PIN set successfully', 200);
  } catch (error) {
    logger.error('Error setting up PIN', { error });
    sendError(res, 'Failed to set up PIN', 500);
  }
}


export const resetWalletPin = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { newPin, otpCode } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!newPin || !otpCode) {
      return sendError(res, 'New PIN and OTP code are required', 400);
    }

    // Validate new PIN format (should be 4-6 digits)
    if (!/^\d{4,6}$/.test(newPin)) {
      return sendError(res, 'New PIN must be 4-6 digits', 400);
    }

    // Validate OTP format
    if (typeof otpCode !== 'string' || otpCode.length !== OTP_CONFIG.LENGTH) {
      return sendError(res, `OTP must be ${OTP_CONFIG.LENGTH} digits`, 400);
    }

    // Check if user has a wallet
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet) {
      return sendError(res, 'No wallet found', 404);
    }

    // Verify OTP for PIN reset (using 'password_reset' type as it's similar)
    const otpVerification = await verifyOTPCode(parseInt(userId), otpCode, 'password_reset');
    if (!otpVerification.isValid) {
      logger.warn('OTP verification failed during PIN reset', {
        userId,
        otpCode,
        error: otpVerification.error
      });
      return sendError(res, otpVerification.error || 'Invalid OTP code', 400);
    }

    // Mark OTP as used
    if (otpVerification.otpRecord) {
      await markOTPAsUsed(otpVerification.otpRecord.id);
    }

    // Hash the new PIN
    const hashedNewPin = await hashPin(newPin);

    // Update the PIN
    const success = await setWalletMasterPin(parseInt(userId), hashedNewPin);

    if (!success) {
      return sendError(res, 'Failed to reset wallet PIN', 500);
    }

    logger.info('Wallet PIN reset successfully', { userId });

    sendUserSuccess(res, 'PIN', 'RESET_SUCCESS', {
      message: 'PIN reset successfully'
    });

  } catch (error) {
    logger.error('Error resetting wallet PIN', { error, userId: req.userId });
    sendError(res, 'Failed to reset wallet PIN', 500);
  }
};


export const changeWalletPin = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { currentPin, newPin, otpCode } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!currentPin || !newPin || !otpCode) {
      return sendError(res, 'Current PIN, new PIN, and OTP code are required', 400);
    }

    // Validate new PIN format (should be 4-6 digits)
    if (!/^\d{4,6}$/.test(newPin)) {
      return sendError(res, 'New PIN must be 4-6 digits', 400);
    }

    // Validate OTP format
    if (typeof otpCode !== 'string' || otpCode.length !== OTP_CONFIG.LENGTH) {
      return sendError(res, `OTP must be ${OTP_CONFIG.LENGTH} digits`, 400);
    }

    // Check if user has a wallet
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet || !wallet.wallet_master_pin) {
      return sendError(res, 'No wallet or PIN found', 404);
    }

    // Verify current PIN
    const isCurrentPinValid = await comparePin(currentPin, wallet.wallet_master_pin);
    if (!isCurrentPinValid) {
      logger.warn('Invalid current PIN attempt during PIN change', { userId });
      return sendError(res, 'Current PIN is incorrect', 400);
    }

    // Verify OTP
    const otpVerification = await verifyOTPCode(parseInt(userId), otpCode, 'transaction');
    if (!otpVerification.isValid) {
      logger.warn('OTP verification failed during PIN change', {
        userId,
        otpCode,
        error: otpVerification.error
      });
      return sendError(res, otpVerification.error || 'Invalid OTP code', 400);
    }

    // Mark OTP as used
    if (otpVerification.otpRecord) {
      await markOTPAsUsed(otpVerification.otpRecord.id);
    }

    // Hash the new PIN
    const hashedNewPin = await hashPin(newPin);

    // Update the PIN
    const success = await setWalletMasterPin(parseInt(userId), hashedNewPin);

    if (!success) {
      return sendError(res, 'Failed to change wallet PIN', 500);
    }

    logger.info('Wallet PIN changed successfully with OTP verification', { userId });

    sendSuccess(res, {
      message: 'PIN changed successfully'
    }, 'Wallet PIN changed successfully');

  } catch (error) {
    logger.error('Error changing wallet PIN', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to change wallet PIN', 500);
  }
};
