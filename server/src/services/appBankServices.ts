import { executeQuery, executeQuery<PERSON>ingle, executeUpdate } from '../utils/database';
import logger from '../utils/logger';

/**
 * Add or update a bank account for the user
 * @param params - Bank account details (id for update, rest for insert)
 * @returns Promise<{ updated: boolean, inserted: boolean }>
 */
export async function addOrUpdateBankAccount(params: {
  id?: string;
  bank_name: string;
  account_number: string;
  routing_number: string;
  account_type: string;
  account_holder_name: string;
  userId: string;
}): Promise<{ updated?: boolean; inserted?: boolean }> {
  try {
    const { id, bank_name, account_number, routing_number, account_type, account_holder_name, userId } = params;
    if (id) {
      // Update existing bank account
      const account = await executeQuerySingle('SELECT * FROM tbl_staff_bank_accounts WHERE id = ? AND staff_id = ?', [id, userId]);
      if (!account) {
        return { updated: false };
      }
      await executeUpdate(
        `UPDATE tbl_staff_bank_accounts SET bank_name = ?, account_number = ?, routing_number = ?, account_type = ?, account_holder_name = ? WHERE id = ? AND staff_id = ?`,
        [bank_name, account_number, routing_number, account_type, account_holder_name, id, userId]
      );
      return { updated: true };
    } else {
      // Insert new bank account
      await executeUpdate(
        `INSERT INTO tbl_staff_bank_accounts (staff_id, bank_name, account_number, routing_number, account_type, account_holder_name) VALUES (?, ?, ?, ?, ?, ?)`,
        [userId, bank_name, account_number, routing_number, account_type, account_holder_name]
      );
      return { inserted: true };
    }
  } catch (error) {
    logger.error('Error in addOrUpdateBankAccount', { error });
    throw error;
  }
}

/**
 * List all bank accounts for the user
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getBankAccounts(userId: string): Promise<any[]> {
  try {
    const accounts = await executeQuery('SELECT * FROM tbl_staff_bank_accounts WHERE staff_id = ? ORDER BY id DESC, created_at ASC', [userId]);
    return accounts;
  } catch (error) {
    logger.error('Error in getBankAccounts', { error });
    throw error;
  }
}

/**
 * List all wallet transactions for the user
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getTransactionsHistory(userId: string): Promise<any[]> {
  try {
    const transactions = await executeQuery(
      `SELECT * FROM tbl_wallet_transactions WHERE user_id = ? ORDER BY id DESC, created_at ASC`,
      [userId]
    );
    return transactions;
  } catch (error) {
    logger.error('Error in getTransactionsHistory', { error });
    throw error;
  }
}

/**
 * List wallet info for the user
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getWallets(userId: string): Promise<any[]> {
  try {
    const wallets = await executeQuery(
      `SELECT id , user_id , wallet_unique_id , name , username , balance FROM tbl_masterwallet WHERE user_id = ? ORDER BY id DESC, created_at ASC`,
      [userId]
    );
    return wallets;
  } catch (error) {
    logger.error('Error in getWallets', { error });
    throw error;
  }
}

export async function setPin(userId: string, pin: string): Promise<void> {
  try {
    await executeUpdate(
      `UPDATE tbl_masterwallet SET wallet_master_pin = ? WHERE user_id = ?`,
      [pin, userId]
    );
  } catch (error) {
    logger.error('Error in setUpPin', { error });
    throw error;
  }
}

