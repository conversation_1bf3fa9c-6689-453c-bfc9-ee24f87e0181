import { executeQuery<PERSON>ingle, executeUpdate, executeQuery } from '../utils/database';
import logger from '../utils/logger';
import { getPaymentMethodById, initializeWithdrawalProgress } from './paymentMethodService';

export interface MasterWallet {
  id: number;
  wallet_unique_id: string;
  user_id: number;
  name: string;
  username: string;
  plaid_access_token?: string;
  wallet_master_pin?: string;
  balance: number;
  status_id: number;
  created_at: Date;
  last_updated: Date;
}

export interface WalletTransaction {
  id: number;
  user_id: number;
  amount: number;
  reference_id: string;
  payment_provider: string;
  description: string;
  status_id: number;
  created_at: Date;
  meta_data?: any;
  plaid_transfer_id?: string;
}

export interface BankAccount {
  id: number;
  user_id: number;
  plaid_account_id: string;
  account_mask: string;
  bank_name: string;
  account_type: string;
  is_primary: boolean;
  plaid_access_token?: string;
  created_at: Date;
}

/**
 * Check if user requires PIN setup
 * @param userId - User ID
 * @returns Promise<boolean> - true if PIN setup is required, false if PIN is already set
 */
export async function checkPinSetupRequired(userId: number): Promise<boolean> {
  try {
    const walletInfo = await executeQuerySingle<{ wallet_master_pin?: string }>(
      'SELECT wallet_master_pin FROM tbl_masterwallet WHERE user_id = ? LIMIT 1',
      [userId]
    );

    // If wallet_master_pin is null, empty, or wallet doesn't exist, user needs PIN setup
    const requiresSetup = !walletInfo || !walletInfo.wallet_master_pin || walletInfo.wallet_master_pin.trim() === '';

    logger.info('PIN setup check completed', {
      userId,
      requiresSetup,
      hasWallet: !!walletInfo
    });

    return requiresSetup;
  } catch (error) {
    logger.error('Error checking PIN setup status', { userId, error });
    // Return true (requires setup) on error to be safe
    return true;
  }
}
/**
 *
 Get user's master wallet
 * @param userId - User ID
 * @returns Promise<MasterWallet | null> - User's master wallet or null if not found
 */
export async function getUserMasterWallet(userId: number): Promise<MasterWallet | null> {
  try {
    const wallet = await executeQuerySingle<MasterWallet>(
      'SELECT * FROM tbl_masterwallet WHERE user_id = ? LIMIT 1',
      [userId]
    );

    return wallet;
  } catch (error) {
    logger.error('Error getting user master wallet', { userId, error });
    return null;
  }
}

/**
 * Create a master wallet for user
 * @param userId - User ID
 * @param pin - Hashed PIN for wallet
 * @returns Promise<MasterWallet | null> - Created wallet or null if failed
 */
export async function createMasterWallet(userId: number, pin?: string): Promise<MasterWallet | null> {
  try {
    // Check if wallet already exists
    const existingWallet = await getUserMasterWallet(userId);
    if (existingWallet) {
      logger.warn('Wallet already exists for user', { userId });
      return existingWallet;
    }

    // Get user details for wallet name
    const user = await executeQuerySingle(
      'SELECT full_name, email FROM tbl_users WHERE id = ?',
      [userId]
    );

    if (!user) {
      logger.error('User not found for wallet creation', { userId });
      return null;
    }

    // Create wallet with unique ID
    const walletUniqueId = `wallet_${userId}_${Date.now()}`;
    const walletName = user.full_name || `Wallet ${userId}`;
    const walletUsername = user.email.split('@')[0];

    const result = await executeUpdate(
      `INSERT INTO tbl_masterwallet
       (wallet_unique_id, user_id, name, username, wallet_master_pin, balance, status_id, created_at, last_updated)
       VALUES (?, ?, ?, ?, ?, 0.00, 1, NOW(), NOW())`,
      [walletUniqueId, userId, walletName, walletUsername, pin || null]
    );

    if (!result || !result.insertId) {
      logger.error('Failed to create wallet', { userId });
      return null;
    }

    // Get the created wallet
    return await getUserMasterWallet(userId);
  } catch (error) {
    logger.error('Error creating master wallet', { userId, error });
    return null;
  }
}

/**
 * Set wallet master PIN
 * @param userId - User ID
 * @param pin - Hashed PIN
 * @returns Promise<boolean> - true if successful, false otherwise
 */
export async function setWalletMasterPin(userId: number, pin: string): Promise<boolean> {
  try {
    const result = await executeUpdate(
      'UPDATE tbl_masterwallet SET wallet_master_pin = ?, last_updated = NOW() WHERE user_id = ?',
      [pin, userId]
    );

    return result && result.affectedRows > 0;
  } catch (error) {
    logger.error('Error setting wallet master PIN', { userId, error });
    return false;
  }
}

/**
 * Verify wallet master PIN
 * @param userId - User ID
 * @param pin - PIN to verify
 * @returns Promise<boolean> - true if PIN is valid, false otherwise
 */
export async function verifyWalletMasterPin(userId: number, pin: string): Promise<boolean> {
  try {
    const wallet = await getUserMasterWallet(userId);
    if (!wallet || !wallet.wallet_master_pin) {
      return false;
    }

    const { comparePin } = await import('../models/user');
    return await comparePin(pin, wallet.wallet_master_pin);
  } catch (error) {
    logger.error('Error verifying wallet master PIN', { userId, error });
    return false;
  }
}

/**
 * Update wallet balance
 * @param userId - User ID
 * @param newBalance - New balance
 * @returns Promise<boolean> - true if successful, false otherwise
 */
export async function updateWalletBalance(userId: number, newBalance: number): Promise<boolean> {
  try {
    const result = await executeUpdate(
      'UPDATE tbl_masterwallet SET balance = ?, last_updated = NOW() WHERE user_id = ?',
      [newBalance, userId]
    );

    return result && result.affectedRows > 0;
  } catch (error) {
    logger.error('Error updating wallet balance', { userId, newBalance, error });
    return false;
  }
}

/**
 * Get wallet balance
 * @param userId - User ID
 * @returns Promise<number | null> - Wallet balance or null if not found
 */
export async function getWalletBalance(userId: number): Promise<number | null> {
  try {
    const wallet = await getUserMasterWallet(userId);
    return wallet ? parseFloat(wallet.balance.toString()) : null;
  } catch (error) {
    logger.error('Error getting wallet balance', { userId, error });
    return null;
  }
}

/**
 * Get wallet statistics
 * @param userId - User ID
 * @returns Promise<any> - Wallet statistics
 */
export async function getWalletStats(userId: number): Promise<any> {
  try {
    // Get total transactions
    const totalTransactions = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM tbl_wallet_transactions WHERE user_id = ?',
      [userId]
    );

    // Get total incoming transactions
    const totalIncoming = await executeQuerySingle<{ total: number }>(
      'SELECT SUM(amount) as total FROM tbl_wallet_transactions WHERE user_id = ? AND amount > 0',
      [userId]
    );

    // Get total outgoing transactions
    const totalOutgoing = await executeQuerySingle<{ total: number }>(
      'SELECT SUM(ABS(amount)) as total FROM tbl_wallet_transactions WHERE user_id = ? AND amount < 0',
      [userId]
    );

    // Get recent transactions
    const recentTransactions = await executeQuery(
      'SELECT * FROM tbl_wallet_transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 5',
      [userId]
    );

    return {
      totalTransactions: totalTransactions?.count || 0,
      totalIncoming: totalIncoming?.total || 0,
      totalOutgoing: totalOutgoing?.total || 0,
      recentTransactions: recentTransactions || []
    };
  } catch (error) {
    logger.error('Error getting wallet stats', { userId, error });
    return {
      totalTransactions: 0,
      totalIncoming: 0,
      totalOutgoing: 0,
      recentTransactions: []
    };
  }
}

/**
 * Create wallet transaction
 * @param userId - User ID
 * @param amount - Transaction amount
 * @param description - Transaction description
 * @param type - Transaction type
 * @param referenceId - Reference ID
 * @param metaData - Additional metadata
 * @param statusId - Status ID (1 = completed, 2 = pending, 3 = failed)
 * @param plaidTransferId - Plaid transfer ID (optional)
 * @returns Promise<number | null> - Transaction ID or null if failed
 */
export async function createWalletTransaction(
  userId: number,
  amount: number,
  description: string,
  type: string = 'wallet_transfer',
  referenceId?: string,
  metaData?: any,
  statusId: number = 1, // Default to completed (1), can be set to pending (2) or failed (3)
  plaidTransferId?: string
): Promise<number | null> {
  try {
    const result = await executeUpdate(
      `INSERT INTO tbl_wallet_transactions
       (user_id, type, amount, reference_id, payment_provider, description, status_id, created_at, meta_data, plaid_transfer_id)
       VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?)`,
      [
        userId,
        type,
        amount,
        referenceId || `TXN_${Date.now()}_${userId}`,
        type,
        description,
        statusId,
        metaData ? JSON.stringify(metaData) : null,
        plaidTransferId || null
      ]
    );

    return result && result.insertId ? result.insertId : null;
  } catch (error) {
    logger.error('Error creating wallet transaction', { userId, amount, type, error });
    return null;
  }
}

/**
 * Get wallet transactions
 * @param userId - User ID
 * @param limit - Limit
 * @param offset - Offset
 * @returns Promise<WalletTransaction[]> - Wallet transactions
 */
export async function getWalletTransactions(
  userId: number,
  limit: number = 50,
  offset: number = 0
): Promise<WalletTransaction[]> {
  try {
    // Get both completed (status_id = 1) and pending (status_id = 2) transactions
    // Use string interpolation for LIMIT and OFFSET to avoid prepared statement issues
    const query = `SELECT * FROM tbl_wallet_transactions
       WHERE user_id = ? AND status_id IN (1, 2)
       ORDER BY created_at DESC
       LIMIT ${limit} OFFSET ${offset}`;
    
    const transactions = await executeQuery<WalletTransaction>(query, [userId]);

    // Debug log to check if there are any transactions
    logger.info('Retrieved wallet transactions', {
      userId,
      transactionCount: Array.isArray(transactions) ? transactions.length : (transactions ? 1 : 0)
    });

    return Array.isArray(transactions) ? transactions : (transactions ? [transactions] : []);
  } catch (error) {
    logger.error('Error getting wallet transactions', { userId, limit, offset, error });
    return [];
  }
}

/**
 * Get primary bank account
 * @param userId - User ID
 * @returns Promise<BankAccount | null> - Primary bank account or null if not found
 */
export async function getPrimaryBankAccount(userId: number): Promise<BankAccount | null> {
  try {
    const account = await executeQuerySingle<BankAccount>(
      'SELECT * FROM tbl_bank_accounts WHERE user_id = ? AND is_primary = 1 LIMIT 1',
      [userId]
    );

    return account;
  } catch (error) {
    logger.error('Error getting primary bank account', { userId, error });
    return null;
  }
}

/**
 * Create ledger entry
 * @param transactionId - Transaction ID
 * @param type - Entry type
 * @param previousBalance - Previous balance
 * @param newBalance - New balance
 * @returns Promise<boolean> - true if successful, false otherwise
 */
export async function createLedgerEntry(
  transactionId: number,
  type: 'credit' | 'debit',
  previousBalance: number,
  newBalance: number
): Promise<boolean> {
  try {
    const result = await executeUpdate(
      `INSERT INTO tbl_wallet_ledger
       (transaction_id, entry_type, previous_balance, new_balance, created_at)
       VALUES (?, ?, ?, ?, NOW())`,
      [transactionId, type, previousBalance, newBalance]
    );

    return result && result.insertId ? true : false;
  } catch (error) {
    logger.error('Error creating ledger entry', { transactionId, type, error });
    return false;
  }
}

/**
 * Add money to wallet from bank account with PIN verification
 * @param userId - User ID
 * @param amount - Amount to add
 * @param pin - PIN for verification
 * @param bankAccountId - Bank account ID (optional, uses primary if not provided)
 * @returns Promise<{success: boolean, newBalance?: number, message?: string, transactionId?: number}>
 */
export async function addMoneyFromBank(
  userId: number,
  amount: number,
  pin: string,
  bankAccountId?: string
): Promise<{ success: boolean, newBalance?: number, message?: string, transactionId?: number }> {
  try {
    // Get user's wallet
    const wallet = await getUserMasterWallet(userId);
    if (!wallet) {
      return { success: false, message: 'Wallet not found' };
    }

    // Verify PIN
    const isPinValid = await verifyWalletMasterPin(userId, pin);
    if (!isPinValid) {
      return { success: false, message: 'Invalid PIN' };
    }

    // Get bank account
    let bankAccount;
    if (bankAccountId) {
      bankAccount = await executeQuerySingle(
        'SELECT * FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
        [bankAccountId, userId]
      );
    } else {
      bankAccount = await getPrimaryBankAccount(userId);
    }

    if (!bankAccount) {
      return { success: false, message: 'Bank account not found' };
    }

    // Use Plaid for real-time transfer
    try {
      // Import the Plaid transfer service and enhanced pending balance service
      const { createACHDebitTransfer } = await import('./plaidTransferService');
      const { createPendingDeposit, getEnhancedWalletBalance } = await import('./enhancedPendingBalanceService');

      // Create the transfer using Plaid
      const plaidResult = await createACHDebitTransfer(
        userId,
        amount,
        `Add money to wallet from ${bankAccount.bank_name}`,
        bankAccount.id.toString()
      );

      if (!plaidResult.success) {
        return { success: false, message: plaidResult.message || 'Failed to add money from bank' };
      }

      // Create pending deposit instead of immediately updating balance
      const pendingResult = await createPendingDeposit(
        userId,
        amount,
        plaidResult.transferId || `ADD_MONEY_${Date.now()}`,
        `Add money from ${bankAccount.bank_name}`,
        {
          bankAccountId: bankAccount.id,
          bankName: bankAccount.bank_name,
          accountMask: bankAccount.account_mask,
          plaidTransferId: plaidResult.transferId,
          plaidStatus: plaidResult.status
        }
      );

      if (!pendingResult.success) {
        return { success: false, message: 'Failed to create pending deposit' };
      }

      // Create transaction record with pending status
      const transactionId = await createWalletTransaction(
        userId,
        amount,
        `Add money from ${bankAccount.bank_name}`,
        'bank_transfer',
        plaidResult.transferId || `ADD_MONEY_${Date.now()}`,
        {
          bankAccountId: bankAccount.id,
          bankName: bankAccount.bank_name,
          accountMask: bankAccount.account_mask,
          plaidTransferId: plaidResult.transferId,
          plaidStatus: plaidResult.status,
          holdId: pendingResult.holdId
        },
        2, // status_id = 2 for pending
        plaidResult.transferId // plaid_transfer_id
      ) || 0;

      // Get current balance info for response
      const balanceInfo = await getEnhancedWalletBalance(userId);
      const currentBalance = balanceInfo?.balance || 0;

      logger.info('Pending deposit created for add money', {
        userId,
        amount,
        plaidTransferId: plaidResult.transferId,
        transactionId,
        holdId: pendingResult.holdId
      });

      return {
        success: true,
        newBalance: currentBalance, // Return current balance, not updated balance
        transactionId,
        message: `Transfer initiated. ${amount.toFixed(2)} will be added to your wallet once confirmed by your bank.`
      };
    } catch (error) {
      logger.error('Error adding money from bank', { userId, amount, error });
      return { success: false, message: 'Failed to add money from bank' };
    }
  } catch (error) {
    logger.error('Error adding money from bank', { userId, amount, error });
    return { success: false, message: 'Failed to add money from bank' };
  }
}

/**
 * Withdraw money from wallet to bank account with PIN verification
 * @param userId - User ID
 * @param amount - Amount to withdraw
 * @param pin - PIN for verification
 * @param bankAccountId - Bank account ID (optional, uses primary if not provided)
 * @param paymentMethod - Payment method (default: 'ach_standard')
 * @returns Promise<{success: boolean, newBalance?: number, message?: string, transactionId?: number}>
 */
export async function withdrawMoneyToBank(
  userId: number,
  amount: number,
  pin: string,
  bankAccountId?: string,
  paymentMethod: string = 'ach_standard'
): Promise<{ success: boolean, newBalance?: number, message?: string, transactionId?: number }> {
  try {
    logger.info('Starting withdrawal process', { userId, amount, paymentMethod, bankAccountId });

    // Validate amount format - must be a positive number with exactly 2 decimal places
    const amountStr = amount.toString();
    const decimalParts = amountStr.split('.');
    if (amount <= 0 || decimalParts.length > 2 || (decimalParts.length === 2 && decimalParts[1].length !== 2)) {
      logger.warn('Invalid amount format for withdrawal', { userId, amount });
      return { success: false, message: 'Withdrawal failed: amount must be a decimal with 2 places greater than 0, such as 0.10' };
    }

    // Format amount to ensure exactly 2 decimal places
    const formattedAmount = parseFloat(amount.toFixed(2));

    // Get user's wallet
    const wallet = await getUserMasterWallet(userId);
    if (!wallet) {
      logger.error('Wallet not found for user', { userId });
      return { success: false, message: 'Wallet not found' };
    }
    logger.info('Wallet retrieved successfully', { userId, walletId: wallet.id });

    // Verify PIN
    const { comparePin } = await import('../models/user');
    const isPinValid = await comparePin(pin, wallet.wallet_master_pin || '');
    if (!isPinValid) {
      logger.warn('Invalid PIN attempt for withdrawal', { userId });
      return { success: false, message: 'Invalid PIN' };
    }
    logger.info('PIN verification successful', { userId });

    // Import enhanced balance service
    const { getEnhancedWalletBalance, createWithdrawalHold } = await import('./enhancedPendingBalanceService');

    // Check sufficient available balance (not just main balance)
    const balanceInfo = await getEnhancedWalletBalance(userId);
    if (!balanceInfo) {
      logger.error('Failed to get wallet balance info', { userId });
      return { success: false, message: 'Failed to check wallet balance' };
    }

    const currentBalance = balanceInfo.balance;
    const availableBalance = balanceInfo.available_balance;

    if (availableBalance < formattedAmount) {
      logger.warn('Insufficient available balance for withdrawal', {
        userId,
        availableBalance,
        requestedAmount: formattedAmount,
        blockedBalance: balanceInfo.blocked_balance
      });
      return { success: false, message: 'Insufficient available balance' };
    }
    logger.info('Available balance check passed', { userId, availableBalance, requestedAmount: formattedAmount });

    // Get selected bank account or primary bank account
    let bankAccount;
    if (bankAccountId) {
      logger.info('Retrieving specific bank account', { userId, bankAccountId });
      // Get specific bank account by ID
      bankAccount = await executeQuerySingle(
        'SELECT * FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
        [bankAccountId, userId]
      );
      if (!bankAccount) {
        logger.error('Selected bank account not found', { userId, bankAccountId });
        return { success: false, message: 'Selected bank account not found or does not belong to user.' };
      }
    } else {
      logger.info('Retrieving primary bank account', { userId });
      // Fall back to primary bank account
      bankAccount = await getPrimaryBankAccount(userId);
      if (!bankAccount) {
        logger.error('No primary bank account found', { userId });
        return { success: false, message: 'No primary bank account found. Please connect a bank account first.' };
      }
    }
    logger.info('Bank account retrieved successfully', { userId, bankAccountId: bankAccount.id, bankName: bankAccount.bank_name });

    // Get payment method details from database
    logger.info('Retrieving payment method', { paymentMethod });
    const selectedMethod = await getPaymentMethodById(paymentMethod);
    if (!selectedMethod) {
      logger.error('Invalid payment method selected', { paymentMethod });
      return { success: false, message: 'Invalid payment method selected' };
    }
    logger.info('Payment method retrieved successfully', { paymentMethod, methodName: selectedMethod.name, fee: selectedMethod.fee });

    // Calculate processing fee using the new fee calculation logic
    const { calculatePaymentMethodFee } = await import('./paymentMethodService');
    const processingFee = calculatePaymentMethodFee(selectedMethod, amount);

    // Ensure limits are numbers
    const minAmount = parseFloat(selectedMethod.min_amount?.toString() || '0') || 0;
    const maxAmount = parseFloat(selectedMethod.max_amount?.toString() || '0') || 0;

    const totalAmount = amount + processingFee;

    // Check if user has sufficient available balance including fees
    if (availableBalance < totalAmount) {
      return {
        success: false,
        message: `Insufficient available balance. Total amount including ${processingFee > 0 ? `${processingFee.toFixed(2)} fee` : 'no fee'}: ${totalAmount.toFixed(2)}`
      };
    }

    // Validate amount limits
    if (amount < minAmount) {
      return {
        success: false,
        message: `Minimum withdrawal amount for ${selectedMethod.name} is ${minAmount.toFixed(2)}`
      };
    }

    if (maxAmount > 0 && amount > maxAmount) {
      return {
        success: false,
        message: `Maximum withdrawal amount for ${selectedMethod.name} is ${maxAmount.toFixed(2)}`
      };
    }

    // Use Plaid for real-time transfer
    logger.info('Initiating Plaid transfer for withdrawal', { userId, amount, bankAccountId: bankAccount.id });

    try {
      // Import the Plaid transfer service
      const { createACHCreditTransfer } = await import('./plaidTransferService');

      // Convert payment method to Plaid payment method type
      let plaidPaymentMethodType = 'ach';
      if (paymentMethod.includes('wire')) {
        plaidPaymentMethodType = 'wire';
      } else if (paymentMethod.includes('instant')) {
        plaidPaymentMethodType = 'instant';
      }

      // Create the transfer using Plaid
      const plaidResult = await createACHCreditTransfer(
        userId,
        amount,
        `${selectedMethod.name} withdrawal to ${bankAccount.bank_name}`,
        bankAccount.id.toString(),
        plaidPaymentMethodType
      );

      if (!plaidResult.success) {
        logger.error('Plaid transfer failed', { userId, error: plaidResult.message });
        return { success: false, message: plaidResult.message || 'Failed to initiate withdrawal' };
      }

      logger.info('Plaid transfer initiated successfully', {
        userId,
        transferId: plaidResult.transferId,
        status: plaidResult.status
      });

      // Create withdrawal hold instead of immediately updating balance
      const holdResult = await createWithdrawalHold(
        userId,
        totalAmount,
        plaidResult.transferId || `WITHDRAW_${Date.now()}`,
        `${selectedMethod.name} withdrawal to ${bankAccount.bank_name}`,
        {
          bankAccountId: bankAccount.id,
          bankName: bankAccount.bank_name,
          accountMask: bankAccount.account_mask,
          paymentMethod: paymentMethod,
          paymentMethodName: selectedMethod.name,
          withdrawalAmount: amount,
          processingFee: processingFee,
          totalAmount: totalAmount,
          estimatedArrival: selectedMethod.processing_time,
          plaidTransferId: plaidResult.transferId,
          plaidStatus: plaidResult.status
        }
      );

      if (!holdResult.success) {
        logger.error('Failed to create withdrawal hold', { userId, error: holdResult.message });
        return { success: false, message: 'Failed to process withdrawal' };
      }

      // Create transaction record with pending status
      const transactionId = await createWalletTransaction(
        userId,
        -totalAmount, // Negative total amount including fees for withdrawal
        `${selectedMethod.name} withdrawal to ${bankAccount.bank_name}`,
        'bank_transfer',
        plaidResult.transferId || `WITHDRAW_${Date.now()}`,
        {
          bankAccountId: bankAccount.id,
          bankName: bankAccount.bank_name,
          accountMask: bankAccount.account_mask,
          paymentMethod: paymentMethod,
          paymentMethodName: selectedMethod.name,
          withdrawalAmount: amount,
          processingFee: processingFee,
          totalAmount: totalAmount,
          estimatedArrival: selectedMethod.processing_time,
          status: plaidResult.status || 'pending',
          plaidTransferId: plaidResult.transferId,
          plaidStatus: plaidResult.status,
          holdId: holdResult.holdId
        },
        2, // status_id = 2 for pending
        plaidResult.transferId // plaid_transfer_id
      );

      if (!transactionId) {
        logger.error('Failed to create transaction record', { userId });
        return { success: false, message: 'Failed to create transaction record' };
      }
      logger.info('Transaction record created successfully', { userId, transactionId });

      // Get updated balance info for response
      const updatedBalanceInfo = await getEnhancedWalletBalance(userId);
      const newAvailableBalance = updatedBalanceInfo?.available_balance || 0;

      logger.info('Withdrawal hold created successfully', {
        userId,
        transactionId,
        holdId: holdResult.holdId,
        availableBalance: newAvailableBalance
      });

      // Initialize withdrawal progress tracking
      logger.info('Initializing withdrawal progress tracking', { transactionId });
      try {
        const progressInitialized = await initializeWithdrawalProgress(transactionId);
        if (!progressInitialized) {
          logger.warn('Failed to initialize withdrawal progress, but withdrawal was successful', {
            transactionId,
            message: 'Progress tracking initialization returned false'
          });
        } else {
          logger.info('Withdrawal progress tracking initialized successfully', { transactionId });
        }
      } catch (progressError) {
        logger.warn('Failed to initialize withdrawal progress, but withdrawal was successful', {
          transactionId,
          progressError: progressError instanceof Error ? progressError.message : 'Unknown error',
          errorStack: progressError instanceof Error ? progressError.stack : undefined,
          sqlState: (progressError as any)?.sqlState,
          sqlMessage: (progressError as any)?.sqlMessage
        });
        // Don't fail the entire withdrawal if progress tracking fails
      }

      logger.info('Withdrawal initiated with hold', {
        userId,
        amount,
        totalAmount,
        paymentMethod,
        fee: processingFee,
        availableBalance: newAvailableBalance,
        transactionId,
        holdId: holdResult.holdId,
        bankAccount: bankAccount.bank_name
      });

      return {
        success: true,
        newBalance: newAvailableBalance, // Return available balance, not main balance
        transactionId,
        message: `Withdrawal initiated. ${amount.toFixed(2)} will be sent to ${bankAccount.bank_name} once confirmed. ${processingFee > 0 ? `Processing fee: ${processingFee.toFixed(2)}. ` : ''}Expected arrival: ${selectedMethod.processing_time}.`
      };
    } catch (error) {
      logger.error('Error with Plaid transfer', { userId, amount, error });
      return { success: false, message: 'Failed to initiate withdrawal with Plaid' };
    }
  } catch (error) {
    logger.error('Error withdrawing money to bank', {
      userId,
      amount,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined,
      errorName: error instanceof Error ? error.name : undefined,
      sqlState: (error as any)?.sqlState,
      sqlMessage: (error as any)?.sqlMessage,
      errno: (error as any)?.errno,
      code: (error as any)?.code
    });
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to withdraw money. Please try again.'
    };
  }
}

/**
 * Verify PIN for dues payment
 * @param userId - User ID
 * @param pin - PIN to verify
 * @returns Promise<{success: boolean, message?: string}>
 */
export async function verifyPin(userId: string, pin: string): Promise<{ success: boolean, message?: string }> {
  try {
    const userIdNum = parseInt(userId);

    // Get user's wallet
    const wallet = await getUserMasterWallet(userIdNum);
    if (!wallet) {
      return { success: false, message: 'Wallet not found' };
    }

    // Verify PIN
    const { comparePin } = await import('../models/user');
    const isPinValid = await comparePin(pin, wallet.wallet_master_pin || '');

    if (isPinValid) {
      logger.info('PIN verification successful', { userId });
      return { success: true };
    } else {
      logger.warn('Invalid PIN attempt', { userId });
      return { success: false, message: 'Invalid PIN' };
    }
  } catch (error) {
    logger.error('Error verifying PIN', { userId, error });
    return { success: false, message: 'PIN verification failed' };
  }
}
/**
 * Create a pending deposit transaction
 * @param userId - User ID
 * @param amount - Amount to deposit
 * @param sourceType - Source type (e.g., 'plaid', 'manual')
 * @param sourceId - Source ID (e.g., bank account ID)
 * @param transferId - Plaid transfer ID
 * @param description - Optional description
 * @param metadata - Optional additional metadata
 * @returns Promise<{success: boolean, transactionId?: number, message?: string}>
 */
export async function createPendingDeposit(
  userId: number,
  amount: number,
  sourceType: string,
  sourceId: string,
  transferId: string,
  description?: string,
  metadata?: Record<string, any>
): Promise<{ success: boolean; transactionId?: number; message?: string }> {
  try {
    logger.info('Creating pending deposit', { userId, amount, sourceType, sourceId, transferId });

    // Validate inputs
    if (!userId || userId <= 0) {
      return { success: false, message: 'Invalid user ID' };
    }

    if (!amount || amount <= 0) {
      return { success: false, message: 'Amount must be greater than zero' };
    }

    if (!sourceType || !sourceId) {
      return { success: false, message: 'Source information is required' };
    }

    if (!transferId) {
      return { success: false, message: 'Transfer ID is required' };
    }

    // Check if user has a wallet
    const wallet = await getUserMasterWallet(userId);
    if (!wallet) {
      logger.error('Wallet not found for user', { userId });
      return { success: false, message: 'Wallet not found' };
    }

    // Format amount to ensure exactly 2 decimal places
    const formattedAmount = parseFloat(amount.toFixed(2));

    // Prepare metadata
    const metaData = {
      sourceType,
      sourceId,
      transferId,
      status: 'pending',
      ...metadata || {}
    };

    // Create transaction description if not provided
    const transactionDescription = description || `Pending deposit from ${sourceType}`;

    // Create the pending transaction record (status_id = 2 for pending)
    const transactionId = await createWalletTransaction(
      userId,
      formattedAmount,
      transactionDescription,
      'deposit',
      transferId,
      metaData,
      2, // status_id = 2 for pending
      transferId // plaid_transfer_id (transferId is the Plaid transfer ID)
    );

    if (!transactionId) {
      logger.error('Failed to create pending deposit transaction', { userId, amount });
      return { success: false, message: 'Failed to create transaction record' };
    }

    // Log the transaction event
    const { logTransactionEvent } = await import('./auditService');
    await logTransactionEvent(
      'pending_deposit_created',
      transferId,
      userId,
      {
        transactionId,
        amount: formattedAmount,
        sourceType,
        sourceId,
        metadata: metaData
      }
    );

    logger.info('Pending deposit created successfully', {
      userId,
      transactionId,
      amount: formattedAmount,
      transferId
    });

    return {
      success: true,
      transactionId,
      message: `Pending deposit of ${formattedAmount.toFixed(2)} created successfully`
    };
  } catch (error) {
    logger.error('Error creating pending deposit', {
      userId,
      amount,
      sourceType,
      sourceId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    return {
      success: false,
      message: 'Failed to create pending deposit. Please try again.'
    };
  }
}/**

 * Create a pending withdrawal transaction
 * @param userId - User ID
 * @param amount - Amount to withdraw
 * @param destinationType - Destination type (e.g., 'plaid', 'manual')
 * @param destinationId - Destination ID (e.g., bank account ID)
 * @param transferId - Plaid transfer ID
 * @param description - Optional description
 * @param metadata - Optional additional metadata
 * @returns Promise<{success: boolean, transactionId?: number, message?: string, newBalance?: number}>
 */
export async function createPendingWithdrawal(
  userId: number,
  amount: number,
  destinationType: string,
  destinationId: string,
  transferId: string,
  description?: string,
  metadata?: Record<string, any>
): Promise<{ success: boolean; transactionId?: number; message?: string; newBalance?: number }> {
  try {
    logger.info('Creating pending withdrawal', { userId, amount, destinationType, destinationId, transferId });

    // Validate inputs
    if (!userId || userId <= 0) {
      return { success: false, message: 'Invalid user ID' };
    }

    if (!amount || amount <= 0) {
      return { success: false, message: 'Amount must be greater than zero' };
    }

    if (!destinationType || !destinationId) {
      return { success: false, message: 'Destination information is required' };
    }

    if (!transferId) {
      return { success: false, message: 'Transfer ID is required' };
    }

    // Check if user has a wallet
    const wallet = await getUserMasterWallet(userId);
    if (!wallet) {
      logger.error('Wallet not found for user', { userId });
      return { success: false, message: 'Wallet not found' };
    }

    // Format amount to ensure exactly 2 decimal places
    const formattedAmount = parseFloat(amount.toFixed(2));

    // Check if user has sufficient balance
    const currentBalance = parseFloat(wallet.balance.toString()) || 0;
    if (currentBalance < formattedAmount) {
      logger.warn('Insufficient balance for withdrawal', { 
        userId, 
        currentBalance, 
        requestedAmount: formattedAmount 
      });
      return { success: false, message: 'Insufficient balance' };
    }

    // Calculate new balance after withdrawal
    const newBalance = currentBalance - formattedAmount;

    // Prepare metadata
    const metaData = {
      destinationType,
      destinationId,
      transferId,
      status: 'pending',
      ...metadata || {}
    };

    // Create transaction description if not provided
    const transactionDescription = description || `Pending withdrawal to ${destinationType}`;

    // Create the pending transaction record (status_id = 2 for pending)
    const transactionId = await createWalletTransaction(
      userId,
      -formattedAmount, // Negative amount for withdrawal
      transactionDescription,
      'withdrawal',
      transferId,
      metaData,
      2, // status_id = 2 for pending
      transferId // plaid_transfer_id (transferId is the Plaid transfer ID)
    );

    if (!transactionId) {
      logger.error('Failed to create pending withdrawal transaction', { userId, amount });
      return { success: false, message: 'Failed to create transaction record' };
    }

    // Update wallet balance to reserve the funds
    const balanceUpdated = await updateWalletBalance(userId, newBalance);
    if (!balanceUpdated) {
      logger.error('Failed to update wallet balance for pending withdrawal', { 
        userId, 
        newBalance 
      });
      
      // If balance update fails, mark the transaction as failed
      await executeUpdate(
        'UPDATE tbl_wallet_transactions SET status_id = 3 WHERE id = ?',
        [transactionId]
      );
      
      return { success: false, message: 'Failed to update wallet balance' };
    }

    // Create ledger entry
    await createLedgerEntry(transactionId, 'debit', currentBalance, newBalance);

    // Log the transaction event
    const { logTransactionEvent, logBalanceChange } = await import('./auditService');
    await logTransactionEvent(
      'pending_withdrawal_created',
      transferId,
      userId,
      {
        transactionId,
        amount: formattedAmount,
        destinationType,
        destinationId,
        metadata: metaData
      }
    );

    // Log the balance change
    await logBalanceChange(
      userId,
      currentBalance,
      newBalance,
      'Pending withdrawal - funds reserved',
      transferId
    );

    logger.info('Pending withdrawal created successfully', {
      userId,
      transactionId,
      amount: formattedAmount,
      transferId,
      oldBalance: currentBalance,
      newBalance
    });

    return {
      success: true,
      transactionId,
      newBalance,
      message: `Pending withdrawal of ${formattedAmount.toFixed(2)} created successfully`
    };
  } catch (error) {
    logger.error('Error creating pending withdrawal', {
      userId,
      amount,
      destinationType,
      destinationId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    return {
      success: false,
      message: 'Failed to create pending withdrawal. Please try again.'
    };
  }
}

/**
 * Update transaction status based on webhooks
 * @param transferId - Plaid transfer ID (used as reference_id in our system)
 * @param status - New status ('pending', 'completed', 'failed', 'cancelled', 'returned')
 * @param metadata - Additional metadata about the status update
 * @returns Promise<{success: boolean, transaction?: any, message?: string, balanceUpdated?: boolean}>
 */
export async function updateTransactionStatus(
  transferId: string,
  status: string,
  metadata?: Record<string, any>
): Promise<{ success: boolean; transaction?: any; message?: string; balanceUpdated?: boolean }> {
  try {
    logger.info('Updating transaction status', { transferId, status });

    // Validate inputs
    if (!transferId) {
      return { success: false, message: 'Transfer ID is required' };
    }

    if (!status) {
      return { success: false, message: 'Status is required' };
    }

    // Map webhook status to our internal status ID
    const statusMap: Record<string, number> = {
      'pending': 2,
      'processing': 2,
      'completed': 1,
      'posted': 1,
      'settled': 1,
      'failed': 3,
      'cancelled': 3,
      'returned': 3
    };

    const statusId = statusMap[status.toLowerCase()] || 2; // Default to pending if unknown

    // Get the transaction details
    const transaction = await executeQuerySingle(
      `SELECT id, user_id, amount, type, reference_id, meta_data, status_id
       FROM tbl_wallet_transactions
       WHERE reference_id = ?`,
      [transferId]
    );

    if (!transaction) {
      logger.warn('Transaction not found for transfer ID', { transferId });
      return { success: false, message: 'Transaction not found' };
    }

    // If status hasn't changed, no need to update
    if (transaction.status_id === statusId) {
      logger.info('Transaction status already up to date', { 
        transferId, 
        status, 
        statusId 
      });
      return { 
        success: true, 
        transaction, 
        message: 'Status already up to date',
        balanceUpdated: false
      };
    }

    // Parse metadata from transaction
    const existingMetadata = typeof transaction.meta_data === 'string'
      ? JSON.parse(transaction.meta_data)
      : (transaction.meta_data || {});

    // Merge existing metadata with new metadata
    const updatedMetadata = {
      ...existingMetadata,
      status,
      statusUpdatedAt: new Date().toISOString(),
      ...metadata || {}
    };

    // Update the transaction status
    await executeUpdate(
      `UPDATE tbl_wallet_transactions
       SET status_id = ?,
           meta_data = ?,
           last_updated = NOW()
       WHERE reference_id = ?`,
      [statusId, JSON.stringify(updatedMetadata), transferId]
    );

    // Log the status update
    const { logTransactionEvent } = await import('./auditService');
    await logTransactionEvent(
      'transaction_status_updated',
      transferId,
      transaction.user_id,
      {
        transactionId: transaction.id,
        oldStatus: transaction.status_id,
        newStatus: statusId,
        metadata: updatedMetadata
      }
    );

    // Handle balance updates for completed transactions
    let balanceUpdated = false;
    if (statusId === 1 && transaction.status_id !== 1) {
      // Transaction is now completed
      if (transaction.type === 'deposit' && transaction.amount > 0) {
        // For deposits, add the amount to the wallet balance when completed
        balanceUpdated = await handleCompletedDeposit(transaction);
      }
      // Note: For withdrawals, we already deducted the balance when creating the pending transaction
    }

    logger.info('Transaction status updated successfully', {
      transferId,
      transactionId: transaction.id,
      oldStatus: transaction.status_id,
      newStatus: statusId,
      balanceUpdated
    });

    // Get the updated transaction
    const updatedTransaction = await executeQuerySingle(
      `SELECT id, user_id, amount, type, reference_id, meta_data, status_id
       FROM tbl_wallet_transactions
       WHERE reference_id = ?`,
      [transferId]
    );

    return {
      success: true,
      transaction: updatedTransaction,
      message: `Transaction status updated to ${status}`,
      balanceUpdated
    };
  } catch (error) {
    logger.error('Error updating transaction status', {
      transferId,
      status,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    return {
      success: false,
      message: 'Failed to update transaction status'
    };
  }
}

/**
 * Handle completed deposit by updating wallet balance
 * @param transaction - Transaction object
 * @returns Promise<boolean> - Whether the balance was updated successfully
 */
async function handleCompletedDeposit(transaction: any): Promise<boolean> {
  try {
    const { user_id, amount, id: transactionId, reference_id } = transaction;
    
    // Get current wallet balance
    const wallet = await executeQuerySingle(
      'SELECT balance FROM tbl_masterwallet WHERE user_id = ?',
      [user_id]
    );
    
    if (!wallet) {
      logger.error('Wallet not found for completed deposit', { 
        userId: user_id, 
        transactionId 
      });
      return false;
    }
    
    const currentBalance = parseFloat(wallet.balance.toString()) || 0;
    const depositAmount = Math.abs(parseFloat(amount.toString()) || 0);
    const newBalance = currentBalance + depositAmount;
    
    // Update wallet balance
    const balanceUpdated = await updateWalletBalance(user_id, newBalance);
    if (!balanceUpdated) {
      logger.error('Failed to update wallet balance for completed deposit', { 
        userId: user_id, 
        transactionId 
      });
      return false;
    }
    
    // Create ledger entry
    await createLedgerEntry(transactionId, 'credit', currentBalance, newBalance);
    
    // Log the balance change
    const { logBalanceChange } = await import('./auditService');
    await logBalanceChange(
      user_id,
      currentBalance,
      newBalance,
      'Deposit completed',
      reference_id
    );
    
    logger.info('Deposit completed successfully', {
      userId: user_id,
      transactionId,
      amount: depositAmount,
      oldBalance: currentBalance,
      newBalance
    });
    
    return true;
  } catch (error) {
    logger.error('Error handling completed deposit', {
      transaction,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    
    return false;
  }
}/*
*
 * Handle failed transaction
 * @param transferId - Plaid transfer ID (used as reference_id in our system)
 * @param reason - Reason for failure
 * @param metadata - Additional metadata about the failure
 * @returns Promise<{success: boolean, transaction?: any, message?: string, balanceRestored?: boolean}>
 */
export async function handleFailedTransaction(
  transferId: string,
  reason: string,
  metadata?: Record<string, any>
): Promise<{ success: boolean; transaction?: any; message?: string; balanceRestored?: boolean }> {
  try {
    logger.info('Handling failed transaction', { transferId, reason });

    // Validate inputs
    if (!transferId) {
      return { success: false, message: 'Transfer ID is required' };
    }

    // Get the transaction details
    const transaction = await executeQuerySingle(
      `SELECT id, user_id, amount, type, reference_id, meta_data, status_id
       FROM tbl_wallet_transactions
       WHERE reference_id = ?`,
      [transferId]
    );

    if (!transaction) {
      logger.warn('Transaction not found for transfer ID', { transferId });
      return { success: false, message: 'Transaction not found' };
    }

    // If transaction is already marked as failed, no need to update
    if (transaction.status_id === 3) {
      logger.info('Transaction already marked as failed', { 
        transferId, 
        transactionId: transaction.id 
      });
      return { 
        success: true, 
        transaction, 
        message: 'Transaction already marked as failed',
        balanceRestored: false
      };
    }

    // Parse metadata from transaction
    const existingMetadata = typeof transaction.meta_data === 'string'
      ? JSON.parse(transaction.meta_data)
      : (transaction.meta_data || {});

    // Merge existing metadata with new metadata
    const updatedMetadata = {
      ...existingMetadata,
      status: 'failed',
      failureReason: reason,
      failedAt: new Date().toISOString(),
      ...metadata || {}
    };

    // Update the transaction status to failed (status_id = 3)
    await executeUpdate(
      `UPDATE tbl_wallet_transactions
       SET status_id = 3,
           meta_data = ?,
           last_updated = NOW()
       WHERE reference_id = ?`,
      [JSON.stringify(updatedMetadata), transferId]
    );

    // Log the status update
    const { logTransactionEvent } = await import('./auditService');
    await logTransactionEvent(
      'transaction_failed',
      transferId,
      transaction.user_id,
      {
        transactionId: transaction.id,
        oldStatus: transaction.status_id,
        newStatus: 3,
        reason,
        metadata: updatedMetadata
      }
    );

    // Handle balance restoration for failed withdrawals/payments
    let balanceRestored = false;
    if ((transaction.type === 'withdrawal' || transaction.type === 'payment') && 
        transaction.amount < 0 && 
        transaction.status_id !== 3) {
      // For withdrawals and payments, restore the funds to the wallet when failed
      balanceRestored = await handleFailedWithdrawalOrPayment(transaction, reason);
    }

    logger.info('Failed transaction handled successfully', {
      transferId,
      transactionId: transaction.id,
      type: transaction.type,
      reason,
      balanceRestored
    });

    // Get the updated transaction
    const updatedTransaction = await executeQuerySingle(
      `SELECT id, user_id, amount, type, reference_id, meta_data, status_id
       FROM tbl_wallet_transactions
       WHERE reference_id = ?`,
      [transferId]
    );

    return {
      success: true,
      transaction: updatedTransaction,
      message: `Transaction marked as failed: ${reason}`,
      balanceRestored
    };
  } catch (error) {
    logger.error('Error handling failed transaction', {
      transferId,
      reason,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    return {
      success: false,
      message: 'Failed to handle failed transaction'
    };
  }
}

/**
 * Handle failed withdrawal or payment by restoring wallet balance
 * @param transaction - Transaction object
 * @param reason - Reason for failure
 * @returns Promise<boolean> - Whether the balance was restored successfully
 */
async function handleFailedWithdrawalOrPayment(transaction: any, reason: string): Promise<boolean> {
  try {
    const { user_id, amount, id: transactionId, reference_id } = transaction;
    
    // Get current wallet balance
    const wallet = await executeQuerySingle(
      'SELECT balance FROM tbl_masterwallet WHERE user_id = ?',
      [user_id]
    );
    
    if (!wallet) {
      logger.error('Wallet not found for failed withdrawal/payment', { 
        userId: user_id, 
        transactionId 
      });
      return false;
    }
    
    const currentBalance = parseFloat(wallet.balance.toString()) || 0;
    const restoredAmount = Math.abs(parseFloat(amount.toString()) || 0);
    const newBalance = currentBalance + restoredAmount;
    
    // Update wallet balance
    const balanceUpdated = await updateWalletBalance(user_id, newBalance);
    if (!balanceUpdated) {
      logger.error('Failed to restore wallet balance for failed withdrawal/payment', { 
        userId: user_id, 
        transactionId 
      });
      return false;
    }
    
    // Create ledger entry
    await createLedgerEntry(transactionId, 'credit', currentBalance, newBalance);
    
    // Update transaction description
    await executeUpdate(
      'UPDATE tbl_wallet_transactions SET description = ? WHERE id = ?',
      [
        `${transaction.type === 'withdrawal' ? 'Withdrawal' : 'Payment'} failed: ${reason} (Funds returned)`, 
        transactionId
      ]
    );
    
    // Log the balance change
    const { logBalanceChange } = await import('./auditService');
    await logBalanceChange(
      user_id,
      currentBalance,
      newBalance,
      `${transaction.type === 'withdrawal' ? 'Withdrawal' : 'Payment'} failed - funds returned`,
      reference_id
    );
    
    logger.info(`${transaction.type === 'withdrawal' ? 'Withdrawal' : 'Payment'} failed - funds returned to wallet`, {
      userId: user_id,
      transactionId,
      amount: restoredAmount,
      reason,
      oldBalance: currentBalance,
      newBalance
    });
    
    return true;
  } catch (error) {
    logger.error('Error handling failed withdrawal/payment', {
      transaction,
      reason,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    
    return false;
  }
}