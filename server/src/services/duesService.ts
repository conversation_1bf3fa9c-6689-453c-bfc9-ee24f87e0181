import { executeSecondaryQuery, executeQuery, executeUpdate, executeTransaction } from '../utils/database';
import logger from '../utils/logger';
import {
  sendPaymentSuccessNotification,
  sendPaymentFailedNotification
} from './duesNotificationService';

// Interfaces for dues management

// Staff dues interface (existing)
export interface Due {
  id: string;
  firstname: string;
  email: string;
  contact?: string;
  game_title: string;
  season_name: string;
  amount: number;
  end_date: string;
  status: 'Paid' | 'Pending' | 'Overdue';
  user_id?: string;
  team_id?: string;
  season_id?: string;
  game_id?: string;
  match_id?: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

// Platform dues interface (new)
export interface PlatformDue {
  id: string;
  season_id: string;
  season_name: string;
  league_name?: string;
  division_name?: string;
  game_title: string;
  teams_count: number;
  amount_per_team: number;
  total_amount: number;
  amount_paid: number;
  amount_pending: number;
  due_date: string;
  status: 'Paid' | 'Partial' | 'Pending' | 'Overdue';
  organizer_id: string;
  organizer_name: string;
  created_at?: string;
  updated_at?: string;
}

export interface DuesFilter {
  status?: string;
  search?: string;
  game_id?: string;
  season_id?: string;
  team_id?: string;
  limit?: number;
  offset?: number;
}

// Platform dues filter interface
export interface PlatformDuesFilter {
  status?: string;
  search?: string;
  game_id?: string;
  season_id?: string;
  league_name?: string;
  division_name?: string;
  limit?: number;
  offset?: number;
}

export interface PaymentRequest {
  dueId: string;
  amount: number;
  payerUserId: string;
  recipientUserId: string;
  pin: string;
  description?: string;
}

export interface PaymentResponse {
  success: boolean;
  message: string;
  transactionId?: string;
  newBalance?: number;
  paymentDetails?: {
    amount: number;
    recipient: string;
    timestamp: string;
    reference: string;
  };
}

/**
 * Get dues for a specific user (staff member)
 */
export const getMyDues = async (userId: string, filters: DuesFilter = {}): Promise<{ data: Due[], totalRecords: number }> => {
  try {
    logger.info('Getting dues for user', { userId, filters });

    const query = `
     SELECT 
      u.id,
      u.firstname,
      u.email,
      u.status_id,
      u.enabled,
      CASE 
        WHEN ptp.payment_status = 'paid' THEN 'Paid'
        WHEN sp.final_day_of_payment < CURDATE() THEN 'Overdue'
        ELSE 'Pending'
      END AS status,
      s.id AS season_id,
      s.season_name,
      s.end_date,
      g.game_title,
      us.match_id,
      CASE 
        WHEN pw.wage_unit_id = 1 THEN pw.amount
        WHEN pw.wage_unit_id IS NOT NULL THEN pw.amount * IFNULL(pw.unit_count, 0)
        ELSE 100.00
      END AS amount,
      sp.final_day_of_payment AS end_date,
      CONCAT('Payment for ', g.game_title, ' - ', s.season_name) AS description,
      us.created_at,
      g.user_id AS recipient_user_id
    FROM match_staff us
    JOIN users u ON u.id = us.user_id 
    JOIN user_roles ur ON ur.user_id = u.id AND ur.role_id = 5
    JOIN matches m ON m.id = us.match_id
    JOIN game_groups_matches ggm ON m.game_group_id = ggm.id
    JOIN seasons s ON s.id = ggm.season_id
    JOIN games g ON g.id = ggm.game_id
    JOIN season_plans sp ON sp.season_id = s.id
    LEFT JOIN paywages pw ON pw.user_id = u.id
    LEFT JOIN player_team_payment ptp ON ptp.player_id = u.id AND ptp.season_id = s.id
    WHERE u.id = ?
      ${filters.status && filters.status !== 'All' ? `AND (
        CASE 
          WHEN ptp.payment_status = 'paid' THEN 'Paid'
          WHEN sp.final_day_of_payment < CURDATE() THEN 'Overdue'
          ELSE 'Pending'
        END
      ) = ?` : ''}
      ${filters.search ? `AND (
        s.season_name LIKE ? OR
        g.game_title LIKE ? OR
        u.firstname LIKE ?
      )` : ''}
    GROUP BY
      u.id, s.id, g.id, us.match_id, pw.wage_unit_id, pw.amount, pw.unit_count, ptp.payment_status, sp.final_day_of_payment, us.created_at, g.user_id
    ORDER BY s.id DESC, us.created_at DESC
    ${filters.limit ? `LIMIT ${filters.limit}` : ''}
    ${filters.offset ? `OFFSET ${filters.offset}` : ''}
    `;

    const params = [userId];
    if (filters.status && filters.status !== 'All') {
      params.push(filters.status);
    }
    if (filters.search) {
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    const dues = await executeSecondaryQuery<Due>(query, params);

    // Get total count for pagination
    const countQuery = query.replace(/SELECT[\s\S]*?FROM/, 'SELECT COUNT(*) as total FROM').replace(/ORDER BY[\s\S]*$/, '');
    const countResult = await executeSecondaryQuery<{ total: number }>(countQuery, params);
    const totalRecords = countResult[0]?.total || 0;

    logger.info('Successfully fetched dues', { userId, count: dues.length, totalRecords });

    return { data: dues, totalRecords };
  } catch (error) {
    logger.error('Error getting dues for user', { userId, error });
    throw new Error(`Failed to get dues: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Get awaiting payments for organization admin
 */
export const getAwaitingPayments = async (userId: string, filters: DuesFilter = {}): Promise<{ data: any[], totalRecords: number }> => {
  try {
    logger.info('Getting awaiting payments for admin', { userId, filters });

    const baseFilters = `g.user_id = ?`;
    const params = [userId, userId, userId, userId];

    // Build dynamic filters
    let additionalFilters = '';
    if (filters.game_id) {
      additionalFilters += ` AND g.id = ${filters.game_id}`;
    }
    if (filters.season_id) {
      additionalFilters += ` AND s.id = ${filters.season_id}`;
    }
    if (filters.search) {
      additionalFilters += ` AND (
        s.season_name LIKE '%${filters.search}%' OR
        g.game_title LIKE '%${filters.search}%' OR
        t.team_name LIKE '%${filters.search}%'
      )`;
    }

    const query = `
      SELECT
        JSON_OBJECT(
            'game_id', g.id,
            'season_id', s.id,
            'game_group_id', ggm.id
        ) AS resolved_ids,
        t.id AS team_id,
        t.team_name,
        t.team_logo,
        COUNT(DISTINCT tm.id) AS player_count,
        s.season_name,
        sp.final_day_of_payment AS end_date,
        sports.sports_name,
        sports.players_per_team,
        g.game_title,
        g.id AS game_id,
        IF(ggm.group_teams IS NULL OR ggm.group_teams = '', 0,
            LENGTH(REPLACE(REPLACE(REPLACE(ggm.group_teams, '[', ''), ']', ''), '"', '')) -
            LENGTH(REPLACE(REPLACE(REPLACE(REPLACE(ggm.group_teams, '[', ''), ']', ''), '"', ''), ',', '')) + 1
        ) AS teamCount
      FROM seasons s
      JOIN games g ON g.id = s.game_id AND g.user_id = ? AND g.status_id NOT IN (3, 28)
      JOIN game_groups_matches ggm ON ggm.season_id = s.id AND ggm.status_id NOT IN (3, 28)
      JOIN sports ON g.sport_id = sports.id
      JOIN season_plans sp ON sp.season_id = s.id
      JOIN teams t ON FIND_IN_SET(t.id, REPLACE(REPLACE(REPLACE(ggm.group_teams, '[', ''), ']', ''), '"', '')) > 0
      LEFT JOIN team_members tm ON tm.team_id = t.id AND tm.role_id = 4
      WHERE ${baseFilters} ${additionalFilters}
      GROUP BY
          s.id, s.season_name, ggm.id, t.id, t.team_name, t.team_logo,
          sp.final_day_of_payment, sports.sports_name, sports.players_per_team,
          g.game_title, g.id
      ORDER BY s.id DESC
      ${filters.limit ? `LIMIT ${filters.limit}` : ''}
      ${filters.offset ? `OFFSET ${filters.offset}` : ''}
    `;

    const data = await executeSecondaryQuery(query, params);

    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT t.id) AS totalRecords
      FROM seasons s
      JOIN games g ON g.id = s.game_id AND g.user_id = ? AND g.status_id NOT IN (3, 28)
      JOIN game_groups_matches ggm ON ggm.season_id = s.id AND ggm.status_id NOT IN (3, 28)
      JOIN sports ON g.sport_id = sports.id
      JOIN season_plans sp ON sp.season_id = s.id
      JOIN teams t ON FIND_IN_SET(t.id, REPLACE(REPLACE(REPLACE(ggm.group_teams, '[', ''), ']', ''), '"', '')) > 0
      WHERE ${baseFilters} ${additionalFilters}
    `;

    const countResult = await executeSecondaryQuery<{ totalRecords: number }>(countQuery, params);
    const totalRecords = countResult[0]?.totalRecords || 0;

    logger.info('Successfully fetched awaiting payments', { userId, count: data.length, totalRecords });

    return { data, totalRecords };
  } catch (error) {
    logger.error('Error getting awaiting payments', { userId, error });
    throw new Error(`Failed to get awaiting payments: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Process payment for a due using wallet transfer
 */
export const payDueWithWallet = async (paymentRequest: PaymentRequest): Promise<PaymentResponse> => {
  try {
    logger.info('Processing due payment with wallet', { 
      dueId: paymentRequest.dueId, 
      amount: paymentRequest.amount,
      payerUserId: paymentRequest.payerUserId,
      recipientUserId: paymentRequest.recipientUserId
    });

    // Start transaction
    const transactionQueries = [];

    // 1. Verify payer has sufficient balance
    const payerWallet = await executeQuery(
      'SELECT balance FROM tbl_wallet_ledger WHERE user_id = ? ORDER BY created_at DESC LIMIT 1',
      [paymentRequest.payerUserId]
    );

    if (!payerWallet.length || payerWallet[0].balance < paymentRequest.amount) {
      return {
        success: false,
        message: 'Insufficient wallet balance'
      };
    }

    const currentBalance = payerWallet[0].balance;
    const newBalance = currentBalance - paymentRequest.amount;
    const transactionId = `DUE_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

    // 2. Create wallet transaction for payer (debit)
    transactionQueries.push({
      query: `INSERT INTO tbl_wallet_transactions 
              (user_id, type, amount, reference_id, payment_provider, description, status_id, created_at, meta_data)
              VALUES (?, 'debit', ?, ?, 'wallet_transfer', ?, '1', NOW(), ?)`,
      params: [
        paymentRequest.payerUserId,
        -paymentRequest.amount,
        transactionId,
        paymentRequest.description || 'Due payment',
        JSON.stringify({
          dueId: paymentRequest.dueId,
          recipientUserId: paymentRequest.recipientUserId,
          type: 'due_payment'
        })
      ]
    });

    // 3. Update payer wallet ledger
    transactionQueries.push({
      query: `INSERT INTO tbl_wallet_ledger 
              (user_id, transaction_type, amount, balance, reference_id, description, created_at)
              VALUES (?, 'debit', ?, ?, ?, ?, NOW())`,
      params: [
        paymentRequest.payerUserId,
        -paymentRequest.amount,
        newBalance,
        transactionId,
        paymentRequest.description || 'Due payment'
      ]
    });

    // 4. Create wallet transaction for recipient (credit)
    transactionQueries.push({
      query: `INSERT INTO tbl_wallet_transactions 
              (user_id, type, amount, reference_id, payment_provider, description, status_id, created_at, meta_data)
              VALUES (?, 'credit', ?, ?, 'wallet_transfer', ?, '1', NOW(), ?)`,
      params: [
        paymentRequest.recipientUserId,
        paymentRequest.amount,
        transactionId,
        paymentRequest.description || 'Due payment received',
        JSON.stringify({
          dueId: paymentRequest.dueId,
          payerUserId: paymentRequest.payerUserId,
          type: 'due_payment_received'
        })
      ]
    });

    // 5. Update recipient wallet ledger
    const recipientWallet = await executeQuery(
      'SELECT balance FROM tbl_wallet_ledger WHERE user_id = ? ORDER BY created_at DESC LIMIT 1',
      [paymentRequest.recipientUserId]
    );
    const recipientBalance = (recipientWallet[0]?.balance || 0) + paymentRequest.amount;

    transactionQueries.push({
      query: `INSERT INTO tbl_wallet_ledger 
              (user_id, transaction_type, amount, balance, reference_id, description, created_at)
              VALUES (?, 'credit', ?, ?, ?, ?, NOW())`,
      params: [
        paymentRequest.recipientUserId,
        paymentRequest.amount,
        recipientBalance,
        transactionId,
        paymentRequest.description || 'Due payment received'
      ]
    });

    // 6. Update due payment status in secondary database
    transactionQueries.push({
      query: `INSERT INTO player_team_payment 
              (player_id, season_id, team_id, player_charged_amount, payment_date, payment_status, transaction_reference)
              VALUES (?, ?, ?, ?, NOW(), 'paid', ?)
              ON DUPLICATE KEY UPDATE 
              payment_status = 'paid', 
              payment_date = NOW(), 
              transaction_reference = ?`,
      params: [
        paymentRequest.payerUserId,
        paymentRequest.dueId, // This should be season_id in real implementation
        1, // team_id - should be passed in request
        paymentRequest.amount,
        transactionId,
        transactionId
      ]
    });

    // Execute all queries in transaction
    await executeTransaction(transactionQueries);

    logger.info('Due payment processed successfully', {
      transactionId,
      newBalance,
      payerUserId: paymentRequest.payerUserId,
      recipientUserId: paymentRequest.recipientUserId
    });

    // Send payment success notification
    try {
      await sendPaymentSuccessNotification(
        paymentRequest.payerUserId,
        paymentRequest.dueId,
        transactionId,
        paymentRequest.amount,
        paymentRequest.description || 'Due payment'
      );
    } catch (notificationError) {
      logger.error('Error sending payment success notification', {
        error: notificationError instanceof Error ? notificationError.message : 'Unknown error',
        payerUserId: paymentRequest.payerUserId,
        dueId: paymentRequest.dueId
      });
      // Continue even if notification fails
    }

    return {
      success: true,
      message: 'Payment completed successfully',
      transactionId,
      newBalance,
      paymentDetails: {
        amount: paymentRequest.amount,
        recipient: paymentRequest.recipientUserId,
        timestamp: new Date().toISOString(),
        reference: transactionId
      }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    logger.error('Error processing due payment', {
      dueId: paymentRequest.dueId,
      error: errorMessage
    });

    // Send payment failed notification
    try {
      await sendPaymentFailedNotification(
        paymentRequest.payerUserId,
        paymentRequest.dueId,
        paymentRequest.amount,
        paymentRequest.description || 'Due payment',
        errorMessage
      );
    } catch (notificationError) {
      logger.error('Error sending payment failed notification', {
        error: notificationError instanceof Error ? notificationError.message : 'Unknown error',
        payerUserId: paymentRequest.payerUserId,
        dueId: paymentRequest.dueId
      });
    }

    return {
      success: false,
      message: 'Payment failed. Please try again.'
    };
  }
};

/**
 * Get team details with payment information
 */
export const getTeamDetail = async (teamId: string): Promise<any> => {
  try {
    logger.info('Getting team details', { teamId });

    const query = `
     SELECT
      t.id AS team_id,
      t.team_name,
      t.team_logo,
      s.season_name,
      s.id AS season_id,

      -- Players only
      (
          SELECT JSON_ARRAYAGG(
              JSON_OBJECT(
                  'user_id', u.id,
                  'firstname', u.firstname,
                  'avtar', u.profile_pic,
                  'email', u.email,
                  'role_name', r.role_name,
                  'paid', tm.is_paid,
                  'player_charged_amount', COALESCE(ptp.player_charged_amount, sp.price),
                  'payment_date', ptp.payment_date,
                  'payment_status', COALESCE(ptp.payment_status, 'pending')
              )
          )
          FROM team_members tm
          JOIN users u ON u.id = tm.user_id
          JOIN roles r ON r.id = tm.role_id
          LEFT JOIN player_team_payment ptp ON ptp.team_id = t.id AND ptp.player_id = u.id
          WHERE tm.team_id = t.id AND r.role_name = 'Player'
      ) AS player_users,

      -- Non-players
      (
          SELECT JSON_ARRAYAGG(
              JSON_OBJECT(
                  'user_id', u.id,
                  'firstname', u.firstname,
                  'avtar', u.profile_pic,
                  'email', u.email,
                  'role_name', r.role_name,
                  'paid', tm.is_paid,
                  'player_charged_amount', NULL,
                  'payment_date', NULL
              )
          )
          FROM team_members tm
          JOIN users u ON u.id = tm.user_id
          JOIN roles r ON r.id = tm.role_id
          WHERE tm.team_id = t.id AND r.role_name != 'Player'
      ) AS other_users

    FROM teams t
    JOIN game_groups_matches ggm
      ON FIND_IN_SET(
          t.id,
          REPLACE(REPLACE(REPLACE(ggm.group_teams, '[', ''), ']', ''), '"', '')
      ) > 0
    JOIN seasons s ON s.id = ggm.season_id
    JOIN season_plans sp ON sp.season_id = s.id
    WHERE t.id = ?
    GROUP BY
        t.id,
        t.team_name,
        t.team_logo,
        s.season_name,
        s.id;
    `;

    const rows = await executeSecondaryQuery(query, [teamId]);

    if (!rows.length) {
      throw new Error('Team not found');
    }

    // Get season plan details
    const seasonId = rows[0].season_id;
    const seasonPlan = await executeSecondaryQuery(
      `SELECT id AS season_plan_id, price, plan_date, final_day_of_payment
       FROM season_plans
       WHERE season_id = ?
       ORDER BY plan_date DESC LIMIT 1`,
      [seasonId]
    );

    const playerFee = parseFloat(seasonPlan[0]?.price || 0);
    const final_day_of_payment = seasonPlan[0]?.final_day_of_payment || null;

    // Parse and enhance player data
    const result = rows[0];
    result.final_day_of_payment = final_day_of_payment;
    result.player_fee = playerFee;

    if (result.player_users) {
      result.player_users = JSON.parse(result.player_users);
    }
    if (result.other_users) {
      result.other_users = JSON.parse(result.other_users);
    }

    logger.info('Successfully fetched team details', { teamId, playerCount: result.player_users?.length || 0 });

    return result;
  } catch (error) {
    logger.error('Error getting team details', { teamId, error });
    throw new Error(`Failed to get team details: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Get game seasons for filters
 */
export const getGameSeasons = async (userId: string): Promise<any[]> => {
  try {
    logger.info('Getting game seasons for user', { userId });

    const query = `
      SELECT
        g.id AS game_id,
        g.game_title,
        s.id AS season_id,
        s.season_name,
        ggm.id AS game_group_id,
        ggm.group_name,
        ggm.status_id,
        ggm.created_at
      FROM seasons s
      JOIN games g ON g.id = s.game_id
      JOIN game_groups_matches ggm ON ggm.season_id = s.id
      WHERE g.user_id = ?
        AND ggm.status_id NOT IN (3, 28)
        AND s.status_id NOT IN (3, 28)
        AND g.status_id NOT IN (3, 28)
      ORDER BY s.id DESC, ggm.id DESC
    `;

    const rows = await executeSecondaryQuery(query, [userId]);

    logger.info('Successfully fetched game seasons', { userId, count: rows.length });

    return rows;
  } catch (error) {
    logger.error('Error getting game seasons', { userId, error });
    throw new Error(`Failed to get game seasons: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Get dues summary for dashboard (STAFF DUES)
 */
export const getDuesSummary = async (userId: string): Promise<{
  totalDues: number;
  paidDues: number;
  pendingDues: number;
  overdueDues: number;
  totalAmountDue: number;
  totalAmountPaid: number;
}> => {
  try {
    logger.info('Getting dues summary for user', { userId });

    const { data: dues } = await getMyDues(userId);

    const summary = {
      totalDues: dues.length,
      paidDues: dues.filter(due => due.status === 'Paid').length,
      pendingDues: dues.filter(due => due.status === 'Pending').length,
      overdueDues: dues.filter(due => due.status === 'Overdue').length,
      totalAmountDue: dues.filter(due => due.status !== 'Paid').reduce((sum, due) => sum + due.amount, 0),
      totalAmountPaid: dues.filter(due => due.status === 'Paid').reduce((sum, due) => sum + due.amount, 0)
    };

    logger.info('Successfully calculated dues summary', { userId, summary });

    return summary;
  } catch (error) {
    logger.error('Error getting dues summary', { userId, error });
    throw new Error(`Failed to get dues summary: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Get platform dues summary for dashboard (PLATFORM DUES)
 */
export const getPlatformDuesSummary = async (userId: string): Promise<{
  totalDues: number;
  paidDues: number;
  pendingDues: number;
  overdueDues: number;
  totalAmountDue: number;
  totalAmountPaid: number;
}> => {
  try {
    logger.info('Getting platform dues summary for organizer', { userId });

    // Return dummy data for testing platform dues section
    const dummyData = {
      totalDues: 8,
      paidDues: 3,
      pendingDues: 4,
      overdueDues: 1,
      totalAmountDue: 1250.00,
      totalAmountPaid: 450.00
    };

    logger.info('Platform dues summary calculated (dummy data)', { userId, summary: dummyData });

    return dummyData;
  } catch (error) {
    logger.error('Error getting platform dues summary', { userId, error });
    throw new Error(`Failed to get platform dues summary: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Get platform dues for an organizer (with dummy data)
 */
export const getPlatformDues = async (userId: string, filters: PlatformDuesFilter = {}): Promise<{ data: PlatformDue[], totalRecords: number }> => {
  try {
    logger.info('Getting platform dues for organizer', { userId, filters });

    // Return dummy platform dues data
    const dummyPlatformDues: PlatformDue[] = [
      {
        id: 'platform_1',
        season_id: '1',
        season_name: 'Soccer Premier League 2024',
        league_name: 'Premier League',
        game_title: 'Soccer Championship',
        teams_count: 8,
        amount_per_team: 15.00,
        total_amount: 170.00, // 50 base + (8 * 15) + 0 league fee
        amount_paid: 0.00,
        amount_pending: 170.00,
        due_date: '2024-12-31',
        status: 'Pending',
        organizer_id: userId,
        organizer_name: 'John Organizer',
        created_at: '2024-01-15T10:00:00Z'
      },
      {
        id: 'platform_2',
        season_id: '2',
        season_name: 'Basketball Local League 2024',
        league_name: 'Local League',
        game_title: 'Basketball Tournament',
        teams_count: 6,
        amount_per_team: 12.00,
        total_amount: 122.00, // 50 base + (6 * 12) + 0 league fee
        amount_paid: 122.00,
        amount_pending: 0.00,
        due_date: '2024-11-30',
        status: 'Paid',
        organizer_id: userId,
        organizer_name: 'John Organizer',
        created_at: '2024-02-01T10:00:00Z'
      },
      {
        id: 'platform_3',
        season_id: '3',
        season_name: 'Tennis Championship 2024',
        division_name: 'Division A',
        game_title: 'Tennis Open',
        teams_count: 4,
        amount_per_team: 20.00,
        total_amount: 130.00, // 50 base + (4 * 20) + 0 division fee
        amount_paid: 65.00,
        amount_pending: 65.00,
        due_date: '2024-10-15',
        status: 'Partial',
        organizer_id: userId,
        organizer_name: 'John Organizer',
        created_at: '2024-03-10T10:00:00Z'
      },
      {
        id: 'platform_4',
        season_id: '4',
        season_name: 'Volleyball Summer League 2024',
        league_name: 'Summer League',
        game_title: 'Volleyball Championship',
        teams_count: 10,
        amount_per_team: 10.00,
        total_amount: 175.00, // 50 base + (10 * 10) + 25 league fee
        amount_paid: 0.00,
        amount_pending: 175.00,
        due_date: '2024-08-15',
        status: 'Overdue',
        organizer_id: userId,
        organizer_name: 'John Organizer',
        created_at: '2024-04-01T10:00:00Z'
      }
    ];

    // Apply filters if provided
    let filteredDues = dummyPlatformDues;

    if (filters.status && filters.status !== 'All') {
      filteredDues = filteredDues.filter(due => due.status === filters.status);
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredDues = filteredDues.filter(due =>
        due.season_name.toLowerCase().includes(searchTerm) ||
        due.game_title.toLowerCase().includes(searchTerm) ||
        (due.league_name && due.league_name.toLowerCase().includes(searchTerm))
      );
    }

    // Apply pagination
    const totalRecords = filteredDues.length;
    if (filters.offset) {
      filteredDues = filteredDues.slice(filters.offset);
    }
    if (filters.limit) {
      filteredDues = filteredDues.slice(0, filters.limit);
    }

    logger.info('Successfully fetched platform dues (dummy data)', { userId, count: filteredDues.length, totalRecords });

    return {
      data: filteredDues,
      totalRecords
    };
  } catch (error) {
    logger.error('Error getting platform dues', { userId, error });
    throw new Error(`Failed to get platform dues: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Interfaces for dues detail functionality
export interface DuesDetail {
  id: number;
  name: string;
  email: string;
  contact?: string;
  avatar?: string;
  full_name?: string;
  totalPaymentPaid: number;
  upcomingPayment: number;
  overduePayment: number;
  gameActivityCount: number;
  transactionsCount: number;
  bankAccountsCount: number;
  lastPaidDate?: string;
  created_at: Date;
  updated_at: Date;
  transactions: {
    id: string;
    transactionId: string;
    date: string;
    paidByReceivedFrom: string;
    paidTo: string;
    type: string;
    amount: number;
    status: string;
  }[];
}

/**
 * Get dues payer details following getStaffDetails pattern
 * @param duesPayerId - Dues payer ID
 * @returns Promise<DuesDetail | null>
 */
export async function getDuesDetails(duesPayerId: number): Promise<DuesDetail | null> {
  try {
    logger.info('Fetching dues payer details', { duesPayerId });

    // Get dues payer details from secondary database
    const duesPayerDetails = await executeSecondaryQuery<any>(
      `SELECT 
        u.id,
        CONCAT(COALESCE(u.firstname, ''), ' ', COALESCE(u.lastname, '')) AS name,
        CONCAT(COALESCE(u.firstname, ''), ' ', COALESCE(u.lastname, '')) AS full_name,
        u.email,
        u.contact_number AS contact,
        CASE 
          WHEN u.profile_pic IS NOT NULL AND u.profile_pic != '' 
          THEN u.profile_pic 
          ELSE CONCAT('https://ui-avatars.com/api/?name=', REPLACE(CONCAT(COALESCE(u.firstname, ''), ' ', COALESCE(u.lastname, '')), ' ', '+'))
        END AS avatar,
        u.created_at,
        u.updated_at
      FROM users u
      WHERE u.id = ? AND u.enabled = 1`,
      [duesPayerId]
    );

    logger.info('Dues payer details from database', { 
      duesPayerId, 
      found: duesPayerDetails.length > 0,
      details: duesPayerDetails.length > 0 ? { id: duesPayerDetails[0].id, name: duesPayerDetails[0].name } : null 
    });

    if (!duesPayerDetails.length) {
      logger.warn('Dues payer not found', { duesPayerId });
      return null;
    }

    const payerDetails = duesPayerDetails[0];

    // Get transaction history for this dues payer
    const transactions = await getDuesTransactionHistory(duesPayerId, 10);

    logger.info('Transactions fetched for dues payer details', { 
      duesPayerId, 
      transactionCount: transactions.length 
    });

    // Calculate transaction-based metrics
    const totalPaymentPaid = transactions
      .filter(t => t.status === 'Paid' && t.amount > 0)
      .reduce((sum, t) => sum + t.amount, 0);

    const upcomingPayment = transactions
      .filter(t => t.status === 'Pending')
      .reduce((sum, t) => sum + t.amount, 0);

    const overduePayment = transactions
      .filter(t => t.status === 'Failed')
      .reduce((sum, t) => sum + t.amount, 0);

    // Get bank accounts count
    const bankAccountsCount = await executeQuery<any>(
      'SELECT COUNT(*) as count FROM tbl_dues_bank_accounts WHERE dues_payer_id = ? AND status = "active"',
      [duesPayerId]
    );

    // Get last paid date
    const lastPaidTransaction = transactions
      .filter(t => t.status === 'Paid')
      .sort((a, b) => new Date(b.dateTime).getTime() - new Date(a.dateTime).getTime())[0];

    const duesDetail: DuesDetail = {
      ...payerDetails,
      totalPaymentPaid,
      upcomingPayment,
      overduePayment,
      gameActivityCount: 10, // Keep static for now, similar to staff
      transactionsCount: transactions.length,
      bankAccountsCount: bankAccountsCount[0]?.count || 0,
      lastPaidDate: lastPaidTransaction?.date,
      transactions
    };

    logger.info('Final dues payer data prepared', {
      duesPayerId,
      transactionsCount: duesDetail.transactionsCount,
      totalPaymentPaid: duesDetail.totalPaymentPaid,
      bankAccountsCount: duesDetail.bankAccountsCount
    });

    return duesDetail;
  } catch (error) {
    logger.error('Error fetching dues payer details', { duesPayerId, error });
    throw error;
  }
}

/**
 * Get dues payer's transaction history with pagination and filtering
 * @param duesPayerId - Dues payer ID
 * @param options - Query options for pagination and filtering
 * @returns Promise<{transactions: any[], total: number, hasMore: boolean}>
 */
export async function getDuesTransactions(
  duesPayerId: number,
  options: {
    limit?: number;
    offset?: number;
    search?: string;
    type?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  } = {}
): Promise<{transactions: any[], total: number, hasMore: boolean}> {
  try {
    const {
      limit = 20,
      offset = 0,
      search = '',
      type = '',
      status = '',
      dateFrom = '',
      dateTo = ''
    } = options;

    logger.info('Fetching dues payer transaction history', {
      duesPayerId,
      duesPayerIdType: typeof duesPayerId,
      options
    });

    // Build WHERE conditions - user_id is bigint in database
    let whereConditions = ['wt.user_id = ?'];
    let queryParams: any[] = [duesPayerId];

    // Add search filter
    if (search) {
      whereConditions.push('(wt.description LIKE ? OR wt.reference_id LIKE ?)');
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // Add type filter (payment_provider)
    if (type && type !== 'all') {
      whereConditions.push('wt.payment_provider = ?');
      queryParams.push(type);
    }

    // Add status filter - status_id is varchar(50) in database
    if (status && status !== 'all') {
      const statusMap: { [key: string]: string } = {
        'paid': '1',
        'pending': '2',
        'failed': '3'
      };
      if (statusMap[status]) {
        whereConditions.push('wt.status_id = ?');
        queryParams.push(statusMap[status]);
      }
    }

    // Add date range filters
    if (dateFrom) {
      whereConditions.push('DATE(wt.created_at) >= ?');
      queryParams.push(dateFrom);
    }
    if (dateTo) {
      whereConditions.push('DATE(wt.created_at) <= ?');
      queryParams.push(dateTo);
    }

    const whereClause = whereConditions.join(' AND ');

    logger.info('Built WHERE clause for dues payer transactions', {
      duesPayerId,
      whereClause,
      queryParams,
      whereConditions
    });

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM tbl_wallet_transactions wt
      WHERE ${whereClause}
    `;

    logger.info('Executing count query', { countQuery, queryParams });
    const countResult = await executeQuery<{total: number}>(countQuery, queryParams);
    const total = countResult[0]?.total || 0;

    logger.info('Count query result', { total, countResult });

    // Get transactions with pagination
    const transactionsQuery = `
      SELECT
        wt.id,
        wt.user_id,
        wt.type,
        wt.amount,
        wt.reference_id,
        wt.payment_provider,
        wt.description,
        wt.status_id,
        wt.created_at,
        wt.meta_data
      FROM tbl_wallet_transactions wt
      WHERE ${whereClause}
      ORDER BY wt.created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    logger.info('Executing transactions query', { transactionsQuery, queryParams });

    const transactions = await executeQuery<any>(
      transactionsQuery,
      queryParams
    );

    logger.info('Raw transactions from database', {
      duesPayerId,
      transactionCount: transactions?.length,
      total,
      hasMore: (offset + limit) < total
    });

    if (!transactions || transactions.length === 0) {
      return { transactions: [], total, hasMore: false };
    }

    // Transform the raw data to match the expected format (same as staff)
    const formattedTransactions = transactions.map((wt: any) => {
      let paidByReceivedFrom = 'Unknown';
      let paidTo = 'Unknown';
      let transactionType = 'Transaction';

      // Parse meta_data if it exists
      let metaData = {};
      if (wt.meta_data) {
        try {
          metaData = typeof wt.meta_data === 'string' ? JSON.parse(wt.meta_data) : wt.meta_data;
        } catch (e) {
          logger.warn('Failed to parse meta_data', { duesPayerId, transactionId: wt.id, meta_data: wt.meta_data });
        }
      }

      // Handle different payment providers and transaction types
      if (wt.payment_provider === 'wallet_transfer') {
        if (wt.type === 'credit' && wt.amount > 0) {
          // Incoming transfer
          paidByReceivedFrom = (metaData as any).senderName || `User ${(metaData as any).senderUserId || 'Unknown'}`;
          paidTo = 'My Wallet';
          transactionType = 'Wallet Transfer (Received)';
        } else if (wt.type === 'debit' && wt.amount < 0) {
          // Outgoing transfer
          paidByReceivedFrom = 'My Wallet';
          paidTo = (metaData as any).recipientName || `User ${(metaData as any).recipientUserId || 'Unknown'}`;
          transactionType = 'Wallet Transfer (Sent)';
        }
      } else if (wt.payment_provider === 'bank_transfer') {
        if (wt.amount > 0) {
          paidByReceivedFrom = 'Bank Transfer';
          paidTo = 'My Wallet';
          transactionType = 'Add Money';
        } else {
          paidByReceivedFrom = 'My Wallet';
          paidTo = 'Bank Account';
          transactionType = 'Withdrawal';
        }
      } else if (wt.payment_provider === 'dues_bank_transfer') {
        if (wt.amount > 0) {
          paidByReceivedFrom = 'Dues Payment';
          paidTo = 'My Wallet';
          transactionType = 'Dues Payment Received';
        } else {
          paidByReceivedFrom = 'My Wallet';
          paidTo = 'Bank Account';
          transactionType = 'Dues Bank Transfer';
        }
      } else {
        // Default case
        paidByReceivedFrom = wt.description || 'Transaction';
        paidTo = wt.amount > 0 ? 'My Wallet' : 'External';
        transactionType = wt.payment_provider || 'Transaction';
      }

      return {
        id: wt.id.toString(),
        transactionId: wt.reference_id || `TXN-${wt.id}`,
        date: wt.created_at ? new Date(wt.created_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        dateTime: wt.created_at ? new Date(wt.created_at).toISOString() : new Date().toISOString(),
        paidByReceivedFrom,
        paidTo,
        type: transactionType,
        amount: Math.abs(parseFloat(wt.amount) || 0),
        status: wt.status_id === '1' ? 'Paid' : wt.status_id === '2' ? 'Pending' : wt.status_id === '3' ? 'Failed' : 'Pending',
        provider: wt.payment_provider,
        description: wt.description,
        metadata: metaData
      };
    });

    const hasMore = (offset + limit) < total;

    logger.info('Formatted dues payer transactions', {
      duesPayerId,
      formattedCount: formattedTransactions.length,
      total,
      hasMore
    });

    return {
      transactions: formattedTransactions,
      total,
      hasMore
    };
  } catch (error) {
    logger.error('Error fetching dues payer transaction history', { duesPayerId, error });
    return { transactions: [], total: 0, hasMore: false };
  }
}

/**
 * Get dues payer's transaction history (simplified version for dues details)
 * @param duesPayerId - Dues payer ID
 * @param limit - Number of transactions to fetch (default: 10)
 * @returns Promise<Transaction[]>
 */
async function getDuesTransactionHistory(duesPayerId: number, limit: number = 10): Promise<any[]> {
  try {
    logger.info('Fetching transaction history for dues payer', { duesPayerId, limit });

    // Query based on the actual database structure
    const transactions = await executeQuery<any>(
      `SELECT
        wt.id,
        wt.user_id,
        wt.type,
        wt.amount,
        wt.reference_id,
        wt.payment_provider,
        wt.description,
        wt.status_id,
        wt.created_at,
        wt.meta_data
       FROM tbl_wallet_transactions wt
       WHERE wt.user_id = ?
       ORDER BY wt.created_at DESC
       LIMIT ?`,
      [duesPayerId, limit]
    );

    logger.info('Raw transactions from database', { 
      duesPayerId, 
      transactionCount: transactions?.length, 
      transactions 
    });

    if (!transactions || transactions.length === 0) {
      logger.info('No transactions found for dues payer', { duesPayerId });
      return [];
    }

    // Transform the raw data to match the expected format (same as staff)
    const formattedTransactions = transactions.map((wt: any) => {
      let paidByReceivedFrom = 'Unknown';
      let paidTo = 'Unknown';
      let transactionType = 'Transaction';

      // Parse meta_data if it exists
      let metaData = {};
      if (wt.meta_data) {
        try {
          metaData = typeof wt.meta_data === 'string' ? JSON.parse(wt.meta_data) : wt.meta_data;
        } catch (e) {
          logger.warn('Failed to parse meta_data', { duesPayerId, transactionId: wt.id, meta_data: wt.meta_data });
        }
      }

      // Handle different payment providers and transaction types
      if (wt.payment_provider === 'wallet_transfer') {
        if (wt.type === 'credit' && wt.amount > 0) {
          // Incoming transfer
          paidByReceivedFrom = (metaData as any).senderName || `User ${(metaData as any).senderUserId || 'Unknown'}`;
          paidTo = 'My Wallet';
          transactionType = 'Wallet Transfer (Received)';
        } else if (wt.type === 'debit' && wt.amount < 0) {
          // Outgoing transfer
          paidByReceivedFrom = 'My Wallet';
          paidTo = (metaData as any).recipientName || `User ${(metaData as any).recipientUserId || 'Unknown'}`;
          transactionType = 'Wallet Transfer (Sent)';
        }
      } else if (wt.payment_provider === 'bank_transfer') {
        if (wt.amount > 0) {
          paidByReceivedFrom = 'Bank Transfer';
          paidTo = 'My Wallet';
          transactionType = 'Add Money';
        } else {
          paidByReceivedFrom = 'My Wallet';
          paidTo = 'Bank Account';
          transactionType = 'Withdrawal';
        }
      } else if (wt.payment_provider === 'dues_bank_transfer') {
        if (wt.amount > 0) {
          paidByReceivedFrom = 'Dues Payment';
          paidTo = 'My Wallet';
          transactionType = 'Dues Payment Received';
        } else {
          paidByReceivedFrom = 'My Wallet';
          paidTo = 'Bank Account';
          transactionType = 'Dues Bank Transfer';
        }
      } else {
        // Default case
        paidByReceivedFrom = wt.description || 'Transaction';
        paidTo = wt.amount > 0 ? 'My Wallet' : 'External';
        transactionType = wt.payment_provider || 'Transaction';
      }

      return {
        id: wt.id.toString(),
        transactionId: wt.reference_id || `TXN-${wt.id}`,
        date: wt.created_at ? new Date(wt.created_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        dateTime: wt.created_at ? new Date(wt.created_at).toISOString() : new Date().toISOString(),
        paidByReceivedFrom,
        paidTo,
        type: transactionType,
        amount: Math.abs(parseFloat(wt.amount) || 0),
        status: wt.status_id === 1 ? 'Paid' : wt.status_id === 2 ? 'Pending' : wt.status_id === 3 ? 'Failed' : 'Pending'
      };
    });

    logger.info('Formatted transactions', { duesPayerId, formattedTransactions });
    return formattedTransactions;
  } catch (error) {
    logger.error('Error fetching dues payer transaction history', { duesPayerId, error });
    return [];
  }
}
