import express from 'express';
import {
  getMyDuesController,
  getDuesSummaryController,
  getAwaitingPaymentsController,
  getTeamDetailController,
  getGameSeasonsController,
  payDueController,
  payBulkDuesController,
  getDuesDetailsController,
  getDuesTransactionsController
} from '../controllers/duesController';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';

const router = express.Router();

// All dues routes require full authentication (JWT + session)
router.use(authenticateTokenAndSession);

// Existing dues routes (keep for backward compatibility)
router.get('/get-Dues', getMyDuesController);              // Get user's dues
router.get('/summary', getDuesSummaryController);          // Get dues summary
router.post('/awaiting-payments', getAwaitingPaymentsController); // Get awaiting payments
router.get('/team/:id', getTeamDetailController);          // Get team details
router.get('/game-seasons', getGameSeasonsController);     // Get game seasons
router.post('/pay', payDueController);                     // Pay single due
router.post('/pay-bulk', payBulkDuesController);           // Pay multiple dues

// New dues detail routes (following staff management pattern)
router.get('/:id/details', getDuesDetailsController);      // Get dues payer details
router.get('/:id/transactions', getDuesTransactionsController); // Get dues payer transactions

export default router;